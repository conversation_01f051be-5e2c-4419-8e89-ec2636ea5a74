# Repository Organization Guide

## Overview

The TikTok Shop project is organized into multiple repositories for better maintainability and development workflow.

## Repository Structure

### Main Repository: `tiktokshop`
- **Purpose**: Central hub and documentation
- **URL**: `https://github.com/vinhWater/tiktokshop`
- **Contains**: Documentation, setup scripts, project overview

### Backend Repository: `tts-be-nestjs`
- **Purpose**: NestJS backend API
- **URL**: `https://github.com/vinhWater/tts-be-nestjs`
- **Contains**: REST API, database models, authentication, TikTok Shop integration

### Frontend Repository: `tts-fe-nextjs`
- **Purpose**: Next.js frontend application
- **URL**: `https://github.com/vinhWater/tts-fe-nextjs`
- **Contains**: Admin dashboard, UI components, state management

### Chrome Extension Repository: `tts-chrome-extension`
- **Purpose**: Chrome extension for product crawling
- **URL**: `https://github.com/vinhWater/tts-chrome-extension`
- **Contains**: Content scripts, popup interface, background workers

## Quick Setup Commands

### Initial Setup (New Developer)

```bash
# Clone main repository
git clone https://github.com/vinhWater/tiktokshop.git
cd tiktokshop

# Clone all component repositories
git clone https://github.com/vinhWater/tts-be-nestjs.git
git clone https://github.com/vinhWater/tts-fe-nextjs.git
git clone https://github.com/vinhWater/tts-chrome-extension.git

# Setup backend
cd tts-be-nestjs
npm install
cp .env.example .env
# Edit .env with your configurations
npm run start:dev

# Setup frontend (new terminal)
cd ../tts-fe-nextjs
npm install
cp .env.example .env.local
# Edit .env.local with your configurations
npm run dev

# Setup Chrome extension (new terminal)
cd ../tts-chrome-extension
# Load unpacked extension in Chrome at chrome://extensions/
```

### Daily Development Workflow

```bash
# Pull latest changes from all repositories
cd tiktokshop
git pull origin main

cd tts-be-nestjs
git pull origin main

cd ../tts-fe-nextjs
git pull origin main

cd ../tts-chrome-extension
git pull origin main
```

## Development Guidelines

### Making Changes

1. **Identify the correct repository** for your changes:
   - API/Database changes → `tts-be-nestjs`
   - UI/Frontend changes → `tts-fe-nextjs`
   - Extension features → `tts-chrome-extension`
   - Documentation → `tiktokshop`

2. **Work in the appropriate repository**:
   ```bash
   cd tts-be-nestjs  # or tts-fe-nextjs or tts-chrome-extension
   git checkout -b feature/your-feature-name
   # Make your changes
   git add .
   git commit -m "Descriptive commit message"
   git push origin feature/your-feature-name
   ```

3. **Create pull requests** in the specific repository

4. **Update documentation** in main repository if needed

### Cross-Repository Changes

When changes affect multiple repositories:

1. **Plan the changes** across repositories
2. **Create feature branches** in each affected repository
3. **Coordinate the changes** to ensure compatibility
4. **Test integration** between components
5. **Merge in the correct order** (usually backend → frontend → extension)

## Port Configuration

- **Backend API**: `http://localhost:3001`
- **Frontend**: `http://localhost:3000`
- **Chrome Extension**: Loaded in browser

## Environment Variables

### Backend (.env)
```
DATABASE_URL=postgresql://username:password@localhost:5432/tiktokshop
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-jwt-secret
TIKTOK_APP_KEY=your-tiktok-app-key
TIKTOK_APP_SECRET=your-tiktok-app-secret
```

### Frontend (.env.local)
```
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret
```

## Testing Strategy

### Backend Testing
```bash
cd tts-be-nestjs
npm run test
npm run test:e2e
```

### Frontend Testing
```bash
cd tts-fe-nextjs
npm run test
npm run test:e2e
```

### Chrome Extension Testing
- Manual testing in Chrome browser
- Test on supported marketplaces (Etsy, eBay, Amazon)
- Verify API integration

## Deployment

### Backend
- Deploy to cloud platform (AWS, Google Cloud, etc.)
- Set up production database and Redis
- Configure environment variables

### Frontend
- Deploy to Vercel, Netlify, or similar
- Configure production environment variables
- Set up custom domain

### Chrome Extension
- Package for Chrome Web Store
- Submit for review and publication
- Maintain version updates

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure backend (3001) and frontend (3000) ports are available
2. **Database connection**: Verify PostgreSQL is running and accessible
3. **Redis connection**: Ensure Redis server is running
4. **Extension loading**: Check manifest.json and file permissions

### Getting Help

1. Check repository-specific README files
2. Review documentation in `docs/` directory
3. Ask team members for assistance
4. Create issues in the appropriate repository

## Best Practices

### Git Workflow
- Use descriptive commit messages
- Create feature branches for new work
- Keep commits focused and atomic
- Write meaningful pull request descriptions

### Code Organization
- Follow established patterns in each repository
- Maintain consistent coding standards
- Write tests for new features
- Update documentation when needed

### Communication
- Coordinate changes that affect multiple repositories
- Document breaking changes
- Notify team of major updates
- Use clear and descriptive branch names

## Repository Maintenance

### Regular Tasks
- Keep dependencies updated
- Monitor security vulnerabilities
- Review and merge pull requests
- Update documentation as needed

### Release Management
- Tag releases in each repository
- Maintain changelog files
- Coordinate releases across repositories
- Test integration before releases
