# Stage 2 – Team Member & User Tier Module

This stage extends **Stage 1 – Store Subscription Module** by adding:
1. Team management (Team creation, member invitation, store assignment)
2. User tier management (<PERSON>er, Seller, Pro-Seller, Big Team)
3. Access control integration to ensure correct permissions

---

## 1. Inheritance from Stage 1
- Use the existing **users**, **tiktok_shops**, **store_subscriptions**, **store_plans** tables.
- Store subscription limits will remain as in Stage 1.
- Each store belongs either to:
  - An **individual user** (no team), OR
  - A **team** (owned by a manager)
- Permissions to perform store actions will now depend on **team membership** and **assigned store roles**.

---

## 2. Business Rules

### 2.1 Team Management
- **1 user can only belong to 1 team** (either as manager or member).
- A **team** is created by a user (Team Owner/Manager).
- Team manager can:
  - Invite members by email.
  - Assign members to manage specific stores in the team.
  - Remove members.
  - Transfer store ownership within the team.

### 2.2 Store Assignment
- A store in a team must be assigned to at least 1 team member (including the manager).
- Only assigned members have permission to operate that store.
- A member cannot access stores they are not assigned to.

### 2.3 User Tier
- **Tiers**:
  1. **Starter**
     - Limit: 30 mockup images / month (global, not per store)
  2. **Seller**
     - Limit: `paid_store_count * 30` mockup images / month
  3. **Pro-Seller**
     - Limit: `paid_store_count * 50` mockup images / month
  4. **Big Team**
     - Limit: `paid_store_count * 60` mockup images / month
- **paid_store_count** = number of stores under user/team that have an active paid subscription.
- Tiers can be upgraded/downgraded manually by admin (future: could be automated).

---

## 3. Database Design

### 3.1 teams
| Column       | Type         | Description |
|--------------|-------------|-------------|
| id           | PK          | Auto ID |
| name         | varchar(255)| Team name |
| owner_id     | FK -> users.id | Team manager |
| created_at   | timestamp   | Creation date |
| updated_at   | timestamp   | Update date |

### 3.2 team_members
| Column       | Type         | Description |
|--------------|-------------|-------------|
| id           | PK          | Auto ID |
| team_id      | FK -> teams.id | The team |
| user_id      | FK -> users.id | The member |
| role         | enum('MANAGER','MEMBER') | Role in team |
| joined_at    | timestamp   | Join date |

> **Rule:** Only 1 MANAGER per team.

### 3.3 team_invitations
| Column       | Type         | Description |
|--------------|-------------|-------------|
| id           | PK          | Auto ID |
| team_id      | FK -> teams.id | The inviting team |
| email        | varchar(255)| Email invited |
| invited_by   | FK -> users.id | User who invited |
| status       | enum('PENDING','ACCEPTED','REJECTED','EXPIRED') | Invitation state |
| token        | varchar(255)| Unique token for joining |
| created_at   | timestamp   | Invitation date |
| expires_at   | timestamp   | Expiration date |

### 3.4 store_assignments
| Column       | Type         | Description |
|--------------|-------------|-------------|
| id           | PK          | Auto ID |
| store_id     | FK -> tiktok_shops.id | Assigned store |
| user_id      | FK -> users.id | Assigned member |
| assigned_at  | timestamp   | Assignment date |

### 3.5 user_tiers
| Column       | Type         | Description |
|--------------|-------------|-------------|
| id           | PK          | Auto ID |
| code         | enum('STARTER','SELLER','PRO_SELLER','BIG_TEAM') | Tier code |
| name         | varchar(255)| Display name |
| mockup_limit_formula | text | Formula for calculating limit |
| created_at   | timestamp   | Created date |

### 3.6 Add to users table
- `tier_id` FK -> user_tiers.id (nullable, default Starter)
- `team_id` FK -> teams.id (nullable)

---

## 4. Access Control Logic

### 4.1 Store Operation Check
For any API request involving a store:
1. **If store.user_id == current_user.id** → Access granted.
2. **Else if store belongs to a team:**
   - Check if `current_user` is in `team_members` for that team.
   - Check if store_id exists in `store_assignments` for that user.
   - If yes → Access granted.
3. Else → Deny access.

### 4.2 Team Management Permissions
- Only team manager can:
  - Invite/remove members
  - Assign/unassign stores
  - Change team name
- Members can:
  - Operate assigned stores
  - View team info

---

## 5. API Endpoints

### 5.1 Team Management
- `POST /teams` – Create a team
- `GET /teams/:id` – Get team details
- `PATCH /teams/:id` – Update team name (manager only)
- `DELETE /teams/:id` – Delete team (only if no stores)

### 5.2 Member Management
- `POST /teams/:id/invite` – Invite member
- `POST /teams/invitations/:token/accept` – Accept invitation
- `POST /teams/invitations/:token/reject` – Reject invitation
- `DELETE /teams/:id/members/:userId` – Remove member

### 5.3 Store Assignment
- `POST /teams/:id/stores/:storeId/assign` – Assign store to member
- `DELETE /teams/:id/stores/:storeId/assign/:userId` – Unassign store from member

### 5.4 User Tier Management
- `GET /tiers` – List available tiers
- `PATCH /users/:id/tier` – Change tier (admin only)
- `GET /users/:id/mockup-limit` – Get current monthly mockup limit

---

## 6. Implementation Steps

1. **Database Migration**
   - Create new tables: teams, team_members, team_invitations, store_assignments, user_tiers
   - Alter users table: add tier_id, team_id

2. **Seed Data**
   - Insert 4 default tiers with formulas.

3. **Access Control Middleware**
   - Implement store operation permission checks.

4. **Team Management APIs**
   - CRUD for team
   - Member invitation flow
   - Store assignment APIs

5. **User Tier APIs**
   - Tier management
   - Mockup limit calculation endpoint

6. **Integration with Stage 1**
   - Store operations should now go through new access control.
   - Subscription still managed per store as in Stage 1.

---

## 7. Notes
- Invoice & payment handling will be implemented in **Stage 3**.
- This stage ensures that all current and future store operations respect team & tier restrictions.

