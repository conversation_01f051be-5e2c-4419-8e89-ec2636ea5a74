# Project Brief

## Core Requirements & Goals

*(Define the fundamental purpose and objectives of the project here. What problem does it solve? What are the key deliverables?)*

I want to create a web application to connect with tiktok shop. This application will manage products, orders from tiktok shops. Then, this application will connect with suppliers via API to automatically fulfill orders.In addition, this web app supports users to search for products on ebay, etsy, amazon... from these products, users will combine with templates defined by the user to create a similar product and then upload it to tiktok shop.

## Scope

*(Outline the boundaries of the project. What is included? What is explicitly excluded?)*

Web application is a type of Software as a Service, with a service fee charged by the user, each user will have many tiktok shops. The system also has many tiktok applications (app key, app secret), the system admin can change the number of tiktok shops connected to each tiktok application.

## Key Stakeholders

*(List the primary individuals or groups involved or affected by the project.)*
I am both the owner of this system and a developer; and I am also the one who uses this system to operate my 7 tiktok shops. And I want to bring this system to provide services to many customers who are tiktok shop owners like me.

## Success Metrics

*(How will we measure the success of this project?)*
The more users, the more connected tiktok shops, the more products, the more orders, the more successful the project is.