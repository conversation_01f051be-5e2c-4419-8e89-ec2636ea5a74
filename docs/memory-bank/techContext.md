# Tech Context

## Technologies Used

*(List the primary programming languages, frameworks, libraries, databases, and other technologies employed in the project.)*

- **Backend:** *(e.g., Node.js with NestJS, Python with Django)*
Framework: NestJS with TypeScript
Database ORM: TypeORM with PostgreSQL
Authentication: JWT with refresh token mechanism
Queue System: Bull for background job processing
Email Service: Nodemailer for sending magic links
- **Frontend:** *(e.g., React, Vue, Angular)*
Framework: Next.js (React)
State management: Zustand for UI state, NextAuth.js for authentication state, React Context for shared data
UI library: Shadcn/ui with Tailwind CSS
Authentication: NextAuth.js with Google OAuth and magic link email authentication
Session management: Custom session context with caching to optimize API calls
- **Database:** *(e.g., PostgreSQL, MongoDB, MySQL)*
PostgreSQL for backend
- **Infrastructure:** *(e.g., AWS, Docker, Kubernetes)*
EC2, RDS of AWS
- **Key Libraries:** *(e.g., TypeORM, Mongoose, Axios)*
Tiktok Shop API SDK Integrate Node.js SDK: https://partner.tiktokshop.com/docv2/page/67c83e11bc0b7d049fb53715

API of Gearment supplier: https://api.gearment.com/#intro


## Development Setup

*(Describe the steps required to set up the local development environment. Include necessary tools, dependencies, and configuration.)*

- **Prerequisites:** *(e.g., Node.js version, Docker Desktop)*
- **Installation:** *(e.g., `npm install`, `docker-compose up`)*
- **Configuration:** *(e.g., Environment variables, `.env` file setup)*
- **Running the App:** *(e.g., `npm run start:dev`)*

## Technical Constraints

*(Outline any limitations or constraints affecting technical decisions, such as performance requirements, budget limitations, or specific platform requirements.)*

Deployment environment should be limited to 1 EC2 server, 1 AWS RDS

## Dependencies

*(List critical external services or systems the project depends on, including APIs, third-party services, etc.)*

## Tool Usage Patterns

*(Describe common tools used in the development workflow, such as linters, formatters, testing frameworks, CI/CD pipelines.)*

- **Linting/Formatting:** *(e.g., ESLint, Prettier)*
- **Testing:** *(e.g., Jest, Mocha, Cypress)*
- **CI/CD:** *(e.g., GitHub Actions, Jenkins)*
