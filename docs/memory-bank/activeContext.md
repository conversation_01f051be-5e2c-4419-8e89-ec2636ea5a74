# Active Context

## Current Focus

*(What is the immediate area of work? What feature or task is being actively developed?)*

Developing both backend (NestJS + PostgreSQL) and frontend (Next.js + NextAuth.js + Zustand).

Current focus is on optimizing API calls and state management in the frontend application. Specifically, we're implementing React Context providers to reduce duplicate API calls and improving session management to minimize calls to `/api/auth/session`.

Completed backend tasks:
Completed entity tiktok application, entity tiktok shop.

Completed CRUD of tiktok shop at Controller, Service level.

Completed get all tiktok shop with query pagination and filter.

Completed create/post new tiktok application, at Controller, Service level. Tiktok application is being integrated with tiktok shop module.

Completed integrating tiktok shop sdk into backend project.

Completed Authorization Flow between backend and tiktok shop. Follow this guide from tiktok: https://partner.tiktokshop.com/docv2/page/678e3a3292b0f40314a92d75

In the Authorization Flow step, I have completed adding the feature of getting the user's tiktok shop list to add to the backend database. Follow this guide from tiktok: https://partner.tiktokshop.com/docv2/page/67c83e11bc0b7d049fb53715

I have completed creating client factory to quickly create client for each tiktok application.

I have completed the constraint that the tiktok ID (idTT) is unique in the Tiktok shop entity. When getting the user's tiktok shop list, if the tiktok id already exists, it will merge the information.

I have added a cron to periodically check the expiration date of the access_token and use refresh_token to request tiktok shop to return a new access token.

I have completed synchronizing tiktok shop products to backend database using ProductV202502Api.ProductsSearchPost.

I have implemented synchronize_detail_tiktok function in product controller to fetch detailed product information using ProductV202309Api.ProductsProductIdGet.

I have enhanced the synchronize_tiktok function to also fetch detailed product information for each product.

I have refactored the ProductsService code by implementing a TikTokProductMapper class, property name utilities, transaction wrapper, and improved error handling and type safety.

## Recent Changes

*(Summarize the most recent significant updates or modifications to the codebase or system.)*

I've added the migration function of typeorm to control schema of database.

I've added sku entity to replace product variant entity.
I've completed processing create/merge productDTO and skuDTO.
I've completed synchronizing ProductV202502Api.ProductsSearchPost of tiktok sdk to product entity.
I've implemented transaction support for all database operations to ensure data consistency.
I've fixed circular reference issues in the product and SKU relationships.
I've standardized field names in the SKU entity (changed snake_case to camelCase).
I've made the listPrice field nullable in the database through a migration.

I've implemented the TikTokProductMapper class to handle data transformation from API responses to DTOs, which improves code maintainability and handles both camelCase and snake_case property names consistently.

I've created property name utilities to simplify accessing properties with different naming conventions.

I've implemented a transaction wrapper utility for consistent database operations across the application.

I've improved error handling and type safety throughout the ProductsService.

I've implemented NextAuth.js for frontend authentication with Google OAuth and magic link email authentication.

I've implemented a robust token refresh mechanism with debounce to prevent multiple simultaneous refresh token requests.

I've organized frontend hooks into separate files based on functionality (e.g., `use-shops.ts` for TikTok shop-related functionality).

I've implemented a clear separation between authentication state (managed by NextAuth) and application state (managed by Zustand).

I've increased the access token expiration time from 1 minute to 15 minutes to reduce the frequency of token refreshes.

I've cleaned up debug logs in both backend and frontend to improve code readability and performance.

I've implemented React Context providers for products, categories, brands, attributes, and warehouses to reduce duplicate API calls and improve state management.

I've optimized session management to reduce calls to `/api/auth/session` by implementing caching and configuring NextAuth's SessionProvider with optimized settings.

I've created comprehensive documentation for session management to help future developers understand the approach and avoid common pitfalls.

## Next Steps

*(What are the planned actions or tasks following the current focus?)*

Implement the function of creating product via ProductV202309Api.ProductsPost. Here is the guide of tiktok sdk: https://partner.tiktokshop.com/docv2/page/6502fc8da57708028b42b18a?external_id=6502fc8da57708028b42b18a#Back%20To%20Top

## Active Decisions & Considerations

*(Document any ongoing decisions, trade-offs, or important points being considered during development.)*

I choose NEST.JS framework for backend because tiktok shop sdk supports Node.js library. https://partner.tiktokshop.com/docv2/page/67c83e0799a75104986ae498

Because of the risk of tiktok application not being approved, I plan to create many tiktok applications, so instead of storing tiktok application in env, I changed it to store in database. https://partner.tiktokshop.com/docv2/page/6789f73f18828103147a8ca1

I've decided to use the sku entity instead of product variant entity for the one-to-many relationship with products.

I've decided to standardize attribute names in the SKU entity (avoiding mixed naming with underscores) and ensure proper data mapping between API responses and database records.

I've decided to apply database transactions for all product operations to ensure data consistency.

I've decided to implement TypeScript migrations to automatically create database enums and add TypeScript migration files directly to the configuration rather than only using compiled JavaScript files.

I've decided to implement a mapper pattern for transforming API responses to DTOs to improve code maintainability and handle both camelCase and snake_case property names consistently.

I've decided to use a transaction utility for consistent database operations across the application.

I've decided to use NextAuth.js as the single source of truth for authentication state and Zustand only for UI state to improve maintainability and reduce complexity.

I've decided to implement a token refresh mechanism with debounce to prevent multiple simultaneous refresh token requests and avoid the "Refresh token revoked" error.

I've decided to increase the access token expiration time from 1 minute to 15 minutes to reduce the frequency of token refreshes while still maintaining security.

## Important Patterns & Preferences

*(Highlight any recurring coding patterns, architectural choices, or developer preferences relevant to the current work.)*

- Using the mapper pattern to transform API responses to DTOs
- Handling both camelCase and snake_case property names consistently
- Using transactions for all database operations
- Implementing proper error handling and type safety
- Breaking down complex methods into smaller, more focused ones
- Organizing frontend hooks into separate files based on functionality
- Using NextAuth.js as the single source of truth for authentication state
- Using Zustand only for UI state and application-specific data
- Implementing debounce patterns for operations that could be triggered multiple times in quick succession
- Cleaning up debug logs after development to improve code readability and performance
- Using React Context providers to centralize state management and reduce duplicate API calls
- Implementing custom hooks that wrap context consumers for better abstraction
- Using a centralized session management approach with NextAuth.js
- Implementing caching strategies to reduce unnecessary API calls
- Documenting complex patterns to help future developers understand the approach

## Learnings & Insights

*(Capture any new understanding, discoveries, or insights gained during the recent development cycle.)*

- TikTok API responses use snake_case for property names, while our internal models use camelCase
- Detailed product information from ProductV202309Api.ProductsProductIdGet provides much richer data than the basic product search
- Proper handling of SKUs requires careful management of relationships and transactions
- TypeScript type safety helps catch potential issues early in the development process
- Multiple simultaneous refresh token requests can lead to "Refresh token revoked" errors if not properly managed
- Implementing a debounce pattern for token refresh operations helps prevent race conditions
- Increasing the access token expiration time from 1 minute to 15 minutes significantly reduces the frequency of token refreshes
- NextAuth.js provides a robust authentication system that integrates well with Next.js applications
- Separating authentication state (NextAuth) from application state (Zustand) improves maintainability
- Organizing frontend hooks into separate files based on functionality helps keep the codebase clean and maintainable
- React Context providers are effective for centralizing state management and reducing duplicate API calls
- NextAuth's SessionProvider can be configured to reduce unnecessary API calls to `/api/auth/session`
- Implementing caching for session data can significantly reduce API calls and improve performance
- Using a single source of truth for session data helps prevent inconsistencies and race conditions
- Documenting complex patterns like session management is crucial for maintainability and onboarding new developers
- Centralizing API calls in context providers helps implement consistent error handling and loading states
