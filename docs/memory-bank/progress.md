# Progress

## What Works

*(List the features or components that are currently functional and stable.)*
Tasks completed:
- Init backend Nest.JS + PostgreSQL successfully
- Commit source code to Github privately.
- Completed tiktok shop module, include CRUD of controller, service and entity.
- Completed get all tiktok shop with pagenation and filter.
- Completed CRUD of tiktok shop at Controller, Service level.
- Completed integrating tiktok shop sdk (node.js) into backend project.
- Completed Authorization Flow between backend and tiktok shop. Follow this guide from tiktok: https://partner.tiktokshop.com/docv2/page/678e3a3292b0f40314a92d75
- Completed create/post new tiktok application, at Controller, Service level. Tiktok application is being integrated with tiktok shop module. Maybe in future , I will add more update/delete/get tiktok application.
- In the Authorization Flow step, I have completed adding the feature of getting the user's tiktok shop list to add to the backend database. Follow this guide from tiktok: https://partner.tiktokshop.com/docv2/page/67c83e11bc0b7d049fb53715
- I have completed creating client factory to quickly create client for each tiktok application.
- I have completed the constraint that the tiktok ID (idTT) is unique in the Tiktok shop entity. When getting the user's tiktok shop list, if the tiktok id already exists, it will merge the information.
- I have added a cron to periodically check the expiration date of the access_token and use refresh_token to request tiktok shop to return a new access token.
- I have completed synchronizing tiktok shop products to backend database using ProductV202502Api.ProductsSearchPost.
- I have implemented transaction support for all database operations to ensure data consistency.
- I have fixed circular reference issues in the product and SKU relationships.
- I have standardized field names in the SKU entity (changed snake_case to camelCase).
- I have made the listPrice field nullable in the database through a migration.
- I have implemented TypeScript migrations to automatically create database enums and added TypeScript migration files directly to the configuration.
- I have implemented synchronize_detail_tiktok function in product controller to fetch detailed product information using ProductV202309Api.ProductsProductIdGet.
- I have enhanced the synchronize_tiktok function to also fetch detailed product information for each product.
- I have refactored the ProductsService code by implementing:
  - TikTokProductMapper class to handle data transformation from API responses to DTOs
  - Property name utilities to handle both camelCase and snake_case property names
  - Transaction wrapper for consistent database operations
  - Improved error handling and type safety
- I have implemented NextAuth.js for frontend authentication with Google OAuth and magic link email authentication.
- I have implemented a robust token refresh mechanism with debounce to prevent multiple simultaneous refresh token requests.
- I have organized frontend hooks into separate files based on functionality (e.g., `use-shops.ts` for TikTok shop-related functionality).
- I have implemented a clear separation between authentication state (managed by NextAuth) and application state (managed by Zustand).
- I have increased the access token expiration time from 1 minute to 15 minutes to reduce the frequency of token refreshes.
- I have implemented React Context providers for products, categories, brands, attributes, and warehouses to reduce duplicate API calls and improve state management.
- I have optimized session management to reduce calls to `/api/auth/session` by implementing caching and configuring NextAuth's SessionProvider with optimized settings.
- I have created comprehensive documentation for session management to help future developers understand the approach and avoid common pitfalls.

## What's Left to Build

*(Outline the remaining features, tasks, or components that need to be implemented.)*
Next tasks:
- I want to implement the function of creating product via ProductV202309Api.ProductsPost. Here is the guide of tiktok sdk: https://partner.tiktokshop.com/docv2/page/6502fc8da57708028b42b18a?external_id=6502fc8da57708028b42b18a#Back%20To%20Top

- Client will provide title, description of products; about product images, client will upload images or provide image urls.

- We will apply asynchronous and setup Bull queue , to enqueue background job of creating product.

- Follow this guide of tiktok sdk of how to creating product based on US: https://partner.tiktokshop.com/docv2/page/650b23eef1fd3102b93d23. Before creating product, we need to : get category, get brand, get attributes, upload size chart, upload product images... all tasks will be done in background job.

## Current Status

*(Provide a snapshot of the project's overall progress. Is it on track? Are there any blockers?)*

The project is on track. We have successfully implemented the functionality to synchronize TikTok Shop products and their detailed information to our database. The code has been refactored for better maintainability and type safety.

We have also implemented a robust authentication system with JWT tokens and refresh token mechanism, both on the backend (NestJS) and frontend (NextAuth.js). The token refresh mechanism has been optimized to prevent issues with multiple simultaneous refresh token requests.

We have significantly improved the frontend architecture by implementing React Context providers for products, categories, brands, attributes, and warehouses to reduce duplicate API calls and improve state management. We have also optimized session management to reduce calls to `/api/auth/session` by implementing caching and configuring NextAuth's SessionProvider with optimized settings.

The next steps involve implementing product creation functionality using TikTok's ProductV202309Api.ProductsPost API with Bull queue for asynchronous background job processing.

## Known Issues

*(Document any bugs, technical debt, or areas needing improvement that are currently known.)*

- **Refresh Token Race Conditions**: While we've implemented debounce to prevent multiple simultaneous refresh token requests, there's still a small possibility of race conditions in high-concurrency scenarios.

- **Error Handling**: Some error handling in the backend could be improved, particularly for edge cases in the TikTok API integration.

- **TypeScript Type Safety**: Some areas of the codebase could benefit from stricter TypeScript typing, especially for TikTok API responses.

- **Test Coverage**: The project would benefit from more comprehensive unit and integration tests, especially for critical paths like authentication and product management.

- **Documentation**: API documentation could be improved with more detailed examples and explanations.

- **Session Management**: While we've optimized session management to reduce calls to `/api/auth/session`, there are still some components that might be triggering unnecessary session checks. Further investigation and optimization could be beneficial.

- **Context Provider Optimization**: The current implementation of context providers could be further optimized to reduce unnecessary re-renders and improve performance.

## Evolution of Project Decisions

*(Track significant changes in direction, scope, or technical approach over the project's lifetime.)*

### Backend Decisions

- Decided to use the sku entity instead of product variant entity for the one-to-many relationship with products.
- Decided to standardize attribute names in the SKU entity (avoiding mixed naming with underscores) and ensure proper data mapping between API responses and database records.
- Decided to apply database transactions for all product operations to ensure data consistency.
- Decided to implement TypeScript migrations to automatically create database enums and add TypeScript migration files directly to the configuration rather than only using compiled JavaScript files.
- Decided to implement a mapper pattern for transforming API responses to DTOs to improve code maintainability and handle both camelCase and snake_case property names consistently.
- Decided to use a transaction utility for consistent database operations across the application.
- Decided to increase the access token expiration time from 1 minute to 15 minutes to reduce the frequency of token refreshes while still maintaining security.
- Decided to implement delayed refresh token revocation (5 seconds) to prevent issues with multiple simultaneous requests.
- Decided to store TikTok applications in the database instead of environment variables to support multiple applications.

### Frontend Decisions

- Decided to use NextAuth.js for authentication instead of a custom solution to leverage its built-in features and community support.
- Decided to use Zustand for UI state management instead of Redux to reduce boilerplate and improve developer experience.
- Decided to implement a debounce pattern for token refresh operations to prevent race conditions.
- Decided to organize frontend hooks into separate files based on functionality to improve code organization.
- Decided to implement a clear separation between authentication state (managed by NextAuth) and application state (managed by Zustand) to improve maintainability.
- Decided to implement React Context providers for products, categories, brands, attributes, and warehouses to reduce duplicate API calls and improve state management.
- Decided to optimize session management by implementing caching and configuring NextAuth's SessionProvider with optimized settings.
- Decided to create comprehensive documentation for session management to help future developers understand the approach and avoid common pitfalls.
