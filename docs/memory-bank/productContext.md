# Product Context

## Problem Space

*(Describe the problem this project aims to solve. Who experiences this problem? What are the pain points?)*

Shop owner sells clothes using the print on demand model on tiktok shop wants a tool to automatically list products on tiktok shop through tiktok's api. Their Product price will be in the form of variants including Price, Color.

Tiktok shop owner wants a tool to automatically track new orders, purchase shipping labels from tiktok and automatically push orders to supplier Gearment.

## Proposed Solution

*(How does this project address the identified problem? What is the core value proposition?)*

The system will connect to Tiktok shop and Gearment supplier via API. The system will have the following feature modules for users:

Tiktop shop management: including connecting the user's tiktok shop to the system, registering for subscription for each tiktok shop, canceling or pausing subscription for each tiktok shop.

Product management: including synchronizing existing products on tiktok, listing new products according to templates on tiktok according to schedule (template details in template management section), manually upgrading/changing each product on tiktok.

Order management: including synchronizing existing order information on tiktok, automatically classifying orders without design and notifying users by email or notification; for orders with design, tiktok's shipping label will be automatically purchased and the order will be automatically pushed through Gearment.

Design management: the list of product variants that do not have a design yet. The user will choose the design according to each variant of the product. Variant here is color, because the design will change according to the color of the product. For example, the same product, but the white shirt will have a different design than the black shirt.

Template management: this template supports users to quickly create products when listing new products on tiktok, users only need to select a template and enter the product name and a list of product images to be able to publish to tiktok shop. The template will include product parameters to inherit as follows: description, size chart image, product image, price of each variation (size, color). For example, some templates are as follows: Gildan 1-sided T-shirts, Gildan 2-sided T-shirts (because 2 sides will have higher prices for each variation compared to 1 side), Gildan 2-sided Sweatshirts, Gildan 2-sided Hoodie, Bella Canvas 1-sided T-shirt, Comfort Color 1-sided T-shirt...

Credit management: include features to top-up, show current credit balance, show history transaction.

Profile management: sign up via email or google account, reset password, sign in

The system will have the following feature modules for admin:

Tiktok Application Management: allow admin define tiktok application such as app key, app secret, redirect url...; number of tiktok shops allowed to connect tiktok application, when the limit is reached, the system will automatically select the next tiktok application; 

Subscription management: currently there is only 1 subscription of 5 usd per month for 1 tiktok shop. users can use free trial of all functions within 3 days.

Customer manager: customer list, filter / search customer by email or name, from the list you can see customer details including current balance, customer transaction history; can reset password and send new password to customer's email.

Global template management: admin will define templates and users can view these templates, choose to copy this global template to user template.

## User Experience Goals

*(What should the user experience feel like? What are the key principles guiding the design and interaction?)*

This is a system with a web interface that can be compatible well on both PC and phone screens.

## Target Audience

*(Who are the primary users of this product? Describe their characteristics and needs.)*

The primary users of this system are Tiktok shop owner which selling clothes based on Print On Demand.