# System Patterns

## Architecture Overview

*(Provide a high-level description of the system's architecture. Is it monolithic, microservices, event-driven, etc.? Include diagrams if helpful.)*

```mermaid
graph TD
    A[User Interface] --> B(API Gateway)
    B --> C{Service A}
    B --> D{Service B}
    C --> E[Database A]
    D --> F[Database B]
```

## Key Technical Decisions

*(Document significant architectural choices and the reasoning behind them. E.g., choice of database, messaging queue, primary framework.)*

### Backend Decisions

- **JWT Authentication**: Using JWT tokens with refresh token mechanism for secure authentication. Access tokens expire after 3 minutes (increased from 1 minute) while refresh tokens last for 7 days.

- **Refresh Token Storage**: Storing refresh tokens in the database with isRevoked flag to track token status. Implementing delayed revocation (5 seconds) to prevent issues with multiple simultaneous requests.

- **Entity Relationships**: Using TypeORM for database interactions with carefully designed entity relationships to prevent circular references and ensure data consistency.

- **Transaction Support**: Implementing transaction support for all database operations to ensure data consistency, especially for complex operations involving multiple entities.

- **Background Processing**: Using Bull queue for asynchronous background job processing, particularly for resource-intensive operations like product creation and image uploads.

- **TypeScript Migrations**: Using TypeScript migrations to automatically create database enums and add TypeScript migration files directly to the configuration rather than only using compiled JavaScript files.

### Frontend Decisions

- **Authentication Strategy**: Using NextAuth.js for centralized authentication with Google OAuth and magic link email authentication. NextAuth serves as the single source of truth for authentication state, while Zustand is used only for UI state.

- **Token Refresh Mechanism**: Implemented a robust token refresh mechanism with debounce to prevent multiple simultaneous refresh token requests. This helps avoid the "Refresh token revoked" error that can occur when multiple API calls trigger token refreshes at the same time.

- **State Management Separation**: Clear separation between authentication state (managed by NextAuth) and application state (managed by Zustand) to improve maintainability and reduce complexity.

- **Hook Organization**: Organized hooks into separate files based on functionality (e.g., `use-shops.ts` for TikTok shop-related functionality) to keep the codebase clean and maintainable.

- **Context Providers**: Implemented React Context providers for products, categories, brands, attributes, and warehouses to reduce duplicate API calls and improve state management. This centralizes data fetching and state management, ensuring consistent data across components.

- **Session Management Optimization**: Optimized session management by implementing caching and configuring NextAuth's SessionProvider with optimized settings to reduce calls to `/api/auth/session`. This improves performance and reduces unnecessary API calls.

- **Documentation**: Created comprehensive documentation for session management to help future developers understand the approach and avoid common pitfalls. This includes detailed explanations of the authentication flow, session management, and best practices.

## Design Patterns in Use

*(List major design patterns employed in the codebase, such as Repository, Service Layer, Dependency Injection, etc.)*

### Backend Patterns

- **Repository Pattern**: Using TypeORM repositories for database access and manipulation.
- **Service Layer Pattern**: Implementing business logic in service classes that are injected into controllers.
- **Dependency Injection**: Using NestJS's built-in DI container for managing dependencies.
- **Factory Pattern**: Implementing a client factory to create TikTok API clients for different applications.
- **Mapper Pattern**: Using mapper classes to transform API responses to DTOs and vice versa.
- **Transaction Pattern**: Implementing transaction support for database operations to ensure data consistency.
- **Queue Pattern**: Using Bull queue for asynchronous background job processing.
- **Observer Pattern**: Implementing event listeners for handling asynchronous events.

### Frontend Patterns

- **Custom Hooks Pattern**: Creating reusable hooks for common functionality.
- **Provider Pattern**: Using context providers for state management.
- **HOC Pattern**: Using higher-order components for authentication and error handling.
- **Debounce Pattern**: Implementing debounce for operations that could be triggered multiple times in quick succession.
- **Singleton Pattern**: Using singleton stores with Zustand for state management.
- **Context Pattern**: Using React Context to share state between components without prop drilling.
- **Adapter Pattern**: Creating adapter hooks that wrap context consumers for better abstraction.
- **Caching Pattern**: Implementing caching strategies to reduce unnecessary API calls.
- **Centralized Session Management Pattern**: Using a single source of truth for session data to prevent inconsistencies.

## Component Relationships

*(Describe how major components or modules interact with each other. Focus on key dependencies and data flows.)*

### Backend Component Relationships

- **Controller → Service → Repository**: Controllers handle HTTP requests and delegate business logic to services, which in turn use repositories for database operations.
- **AuthService → JwtService**: AuthService uses JwtService to generate and verify JWT tokens.
- **AuthService → RefreshTokenRepository**: AuthService manages refresh tokens through the RefreshTokenRepository.
- **ProductsService → TikTokProductMapper**: ProductsService uses TikTokProductMapper to transform API responses to DTOs.
- **TikTokShopService → TikTokClientFactory**: TikTokShopService uses TikTokClientFactory to create TikTok API clients for different applications.
- **ProductUploadQueue → ProductsService**: ProductUploadQueue processes background jobs and uses ProductsService for product creation.

### Frontend Component Relationships

- **NextAuth → API Routes**: NextAuth.js handles authentication through API routes.
- **NextAuth → Backend Auth API**: NextAuth.js communicates with the backend Auth API for token verification and refresh.
- **Zustand Stores → React Components**: Zustand stores provide state to React components.
- **Custom Hooks → API Client**: Custom hooks use the API client to communicate with the backend.
- **API Client → NextAuth Session**: API client uses NextAuth session for authentication tokens.
- **Context Providers → React Components**: Context providers share state with React components without prop drilling.
- **Custom Hooks → Context Consumers**: Custom hooks wrap context consumers to provide a simpler API for components.
- **SessionProvider → CustomSessionProvider**: NextAuth's SessionProvider is wrapped by a custom provider to optimize session management.
- **API Client → Session Cache**: API client uses a session cache to reduce calls to `/api/auth/session`.

## Critical Implementation Paths

*(Highlight core workflows or processes that are central to the system's functionality.)*

### Authentication Flow

1. **User Login**: User logs in through NextAuth.js using Google OAuth or magic link email.
2. **Token Generation**: Backend generates JWT access token and refresh token.
3. **Token Storage**: Frontend stores tokens in NextAuth.js session.
4. **API Requests**: Frontend includes access token in API requests.
5. **Token Refresh**: When access token expires, frontend uses refresh token to get a new access token.
6. **Token Revocation**: When user logs out, refresh token is revoked in the database.

### TikTok Shop Integration Flow

1. **Application Creation**: Admin creates a TikTok application in the system.
2. **Authorization**: User authorizes the application to access their TikTok shop.
3. **Shop Retrieval**: System retrieves user's TikTok shops and stores them in the database.
4. **Token Management**: System manages access tokens and refresh tokens for TikTok API access.
5. **Product Synchronization**: System synchronizes products from TikTok shop to the database.
6. **Product Creation**: User creates products in the system, which are then uploaded to TikTok shop through background jobs.

### Product Management Flow

1. **Product Creation**: User creates a product with basic information.
2. **Image Upload**: User uploads product images or provides image URLs.
3. **Background Processing**: System processes product creation in the background using Bull queue.
4. **TikTok API Integration**: System uploads product to TikTok shop using TikTok API.
5. **Status Tracking**: System tracks product upload status and updates the database accordingly.
6. **Product Synchronization**: System periodically synchronizes products from TikTok shop to ensure data consistency.
