# Stage 1 – Store Subscription Module

## Overview
This stage focuses on implementing the store subscription management module.
It covers:
- Storing and managing store subscription history.
- Assigning a default plan when a store is created.
- Updating/changing the plan of a subscription.
- Quickly retrieving the current plan and its limits.
- Enforcing access control for store subscription management.

## Key Business Logic
1. **Store Subscription Creation**
   - A store can have **only one active subscription** at a time.
   - Plans: `free`, `creator`, `growth`.
   - Each plan has feature limits (see **Store Plans Table** below).
   
2. **Default Plan Assignment**
   - When a store (`tiktok_shops`) is first created, it will be **automatically assigned** to the `Free` plan.
   - A record will be inserted into `store_subscriptions` with:
     - `plan_id` = Free Plan
     - `start_date` = current timestamp
     - `status` = `active`

2. **Subscription Change Tracking**
   - All changes in subscription (upgrade, downgrade, plan switch) will be recorded in `store_subscriptions`.
   - `tiktok_shops.current_store_plan_id` will always reflect the **current active plan** for quick queries.

3. **Store Subscription Change**
   - Changing from one plan to another creates a new record in the **store_subscriptions** table.
   - The previous subscription is marked as **ended**.
   - Status transitions are tracked in `status`.

4. **History Tracking**
   - Keep full subscription history per store.
   - Status change timestamps are recorded.

5. **Plan Limits**
   - Plans and their monthly limits:
     | Plan     | Max Upload Products / Month / Store | Max Auto Fulfill Orders / Month / Store |
     |----------|------------------------------|------------------------------------------|
     | Free     | 10                           | 30                                       |
     | Creator  | 100                          | 300                                      |
     | Growth   | Unlimited (-1)               | Unlimited (-1)                           |
   - Limits will be stored in `store_plans` as numeric columns:
     - `max_upload_products_per_month`
     - `max_fulfill_orders_per_month`
     - `-1` indicates unlimited.

6. **Current Plan Reference**
   - `tiktok_shops` will have a foreign key `current_store_plan_id` → `store_plans.id`
   - This enables fast access to plan limits without joining subscription history.
   - Whenever subscription changes, both `store_subscriptions` and `tiktok_shops.current_store_plan_id` are updated in the same transaction.

7. **Access Control**
   - Only the **store owner** (linked user) can view or change the subscription in Stage 1.

## Database Design

### Table: store_plans
```sql
CREATE TABLE store_plans (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    max_upload_products_per_month INT NOT NULL,
    max_fulfill_orders_per_month INT NOT NULL,
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now()
);
```

### Table: store_subscriptions
```sql
CREATE TABLE store_subscriptions (
    id SERIAL PRIMARY KEY,
    store_id INT NOT NULL REFERENCES tiktok_shops(id) ON DELETE CASCADE,
    plan_id INT NOT NULL REFERENCES store_plans(id),
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NULL,
    status VARCHAR(20) NOT NULL, -- active, ended, canceled
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now()
);
```
**Status Explanation:**
- **active** → currently active subscription.
- **ended** → subscription ended normally due to change/downgrade.
- **canceled** → subscription was terminated before its term ended.

### Updated Table: tiktok_shops
```sql
ALTER TABLE tiktok_shops
ADD COLUMN current_store_plan_id INT REFERENCES store_plans(id);
```

## API Endpoints
1. **GET /stores/{id}/subscription**
   - Returns current subscription and plan limits.
   - Access control: only store owner can view.

2. **POST /stores/{id}/subscription/change**
   - Changes plan for a store.
   - Inserts a new record in `store_subscriptions` and updates `current_store_plan_id`.
   - Ends the old subscription record.

3. **GET /store-plans**
   - Lists all available store plans.

## Implementation Steps
1. Create `store_plans` table with predefined rows (Free, Creator, Growth).
2. Add `current_store_plan_id` column to `tiktok_shops`.
3. Create `store_subscriptions` table.
4. On store creation:
   - Assign Free plan in both `store_subscriptions` and `tiktok_shops.current_store_plan_id`.
5. Implement subscription change service:
   - End the old subscription (`status=ended`, set `end_date`).
   - Create a new subscription record.
   - Update `current_store_plan_id`.
6. Implement API to fetch and change subscription.
7. Implement plan limit check utility function (used later for product upload / order fulfill validation).
