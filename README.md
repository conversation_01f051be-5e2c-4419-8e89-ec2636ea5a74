# TikTok Shop Project

This is the main repository for the TikTok Shop project. The project is split into three separate repositories:

1. **Backend (NestJS)**: [https://github.com/vinhWater/tts-be-nestjs](https://github.com/vinhWater/tts-be-nestjs)
2. **Frontend (NextJS)**: [https://github.com/vinhWater/tts-fe-nextjs](https://github.com/vinhWater/tts-fe-nextjs)
3. **Chrome Extension**: [https://github.com/vinhWater/tts-chrome-extension](https://github.com/vinhWater/tts-chrome-extension)

## Project Overview

This project provides a comprehensive admin dashboard for managing TikTok Shop products, categories, brands, and more. It consists of a NestJS backend, a Next.js frontend, and a Chrome extension for product crawling.

## Features

### Backend (NestJS)
- RESTful API with TypeORM and PostgreSQL
- JWT authentication and authorization
- TikTok Shop API integration
- Product, category, and brand management
- Image upload with Cloudflare R2 storage
- Bull queue for background processing

### Frontend (Next.js)
- Admin dashboard with product management
- Table components with multi-filter, multi-selection, and bulk edit capabilities
- Category and brand management
- TikTok Shop integration
- Authentication system with NextAuth
- Responsive design with Tailwind CSS

### Chrome Extension
- Product extraction from e-commerce marketplaces (Etsy, eBay, Amazon)
- Automated product crawling and data collection
- Integration with backend API for data storage
- Authentication with existing user system

## Tech Stack

### Backend
- **Framework**: NestJS (Node.js)
- **Database**: PostgreSQL with TypeORM
- **Authentication**: JWT with Passport
- **Queue**: Bull (Redis)
- **Storage**: Cloudflare R2
- **API Integration**: TikTok Shop API

### Frontend
- **Framework**: Next.js 14 (React)
- **State Management**: Zustand
- **UI Library**: Shadcn/ui with Tailwind CSS
- **API Client**: Axios with React Query
- **Table**: TanStack Table (React Table)
- **Authentication**: NextAuth.js

### Chrome Extension
- **Manifest**: V3
- **Content Scripts**: Vanilla JavaScript
- **Background**: Service Worker
- **Storage**: Chrome Extension API

## Getting Started

### Prerequisites

- Node.js 18.17.0 or later
- npm or yarn
- PostgreSQL database
- Redis server
- Chrome browser (for extension development)

### Development Setup

This project uses separate repositories for each component. Follow these steps to set up the complete development environment:

#### 1. Clone All Repositories

```bash
# Clone the main repository
git clone https://github.com/vinhWater/tiktokshop.git
cd tiktokshop

# Clone backend repository
git clone https://github.com/vinhWater/tts-be-nestjs.git

# Clone frontend repository
git clone https://github.com/vinhWater/tts-fe-nextjs.git

# Clone chrome extension repository
git clone https://github.com/vinhWater/tts-chrome-extension.git
```

#### 2. Backend Setup (NestJS)

```bash
cd tts-be-nestjs
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your database and API configurations

# Start the backend server
npm run start:dev
```

#### 3. Frontend Setup (Next.js)

```bash
cd ../tts-fe-nextjs
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your API URLs

# Start the frontend server
npm run dev
```

#### 4. Chrome Extension Setup

```bash
cd ../tts-chrome-extension

# Load extension in Chrome
# 1. Open Chrome and go to chrome://extensions/
# 2. Enable "Developer mode"
# 3. Click "Load unpacked" and select the tts-chrome-extension directory
```

### Access Points

- **Frontend**: [http://localhost:3000](http://localhost:3000)
- **Backend API**: [http://localhost:3001](http://localhost:3001)
- **API Documentation**: [http://localhost:3001/api](http://localhost:3001/api)
- **Chrome Extension**: Available in Chrome toolbar after installation

## Repository Structure

This main repository serves as the central hub and documentation for the TikTok Shop project. The actual code is organized in separate repositories:

### Main Repository (`tiktokshop`)
```
tiktokshop/
├── README.md                    # This file - project overview
├── docs/                        # Project documentation
├── tts-be-nestjs/              # Backend repository (git clone)
├── tts-fe-nextjs/              # Frontend repository (git clone)
└── tts-chrome-extension/       # Chrome extension repository (git clone)
```

### Backend Repository (`tts-be-nestjs`)
```
tts-be-nestjs/
├── src/
│   ├── auth/                   # Authentication modules
│   ├── products/               # Product management
│   ├── tiktok-shop/           # TikTok Shop integration
│   ├── providers/             # External provider integrations
│   └── queues/                # Background job processing
├── docs/                      # Backend documentation
└── package.json
```

### Frontend Repository (`tts-fe-nextjs`)
```
tts-fe-nextjs/
├── src/
│   ├── app/                   # Next.js App Router pages
│   ├── components/            # React components
│   │   ├── ui/               # Shadcn UI components
│   │   ├── layout/           # Layout components
│   │   └── products/         # Product-related components
│   └── lib/                  # Utility functions and hooks
│       ├── api/              # API client and services
│       ├── hooks/            # Custom React hooks
│       └── store/            # Zustand state management
└── package.json
```

### Chrome Extension Repository (`tts-chrome-extension`)
```
tts-chrome-extension/
├── manifest.json              # Extension manifest
├── background/               # Background scripts
├── content-scripts/          # Content scripts for marketplaces
├── popup/                    # Extension popup interface
└── icons/                    # Extension icons
```

## Development Workflow

### Working with Multiple Repositories

Since this project spans multiple repositories, here's the recommended workflow:

1. **Make changes in the appropriate repository**:
   - Backend changes: Work in `tts-be-nestjs/`
   - Frontend changes: Work in `tts-fe-nextjs/`
   - Extension changes: Work in `tts-chrome-extension/`

2. **Commit and push to individual repositories**:
   ```bash
   cd tts-be-nestjs
   git add .
   git commit -m "Add new feature"
   git push origin main
   ```

3. **Update documentation in main repository** if needed

### Adding New Features

#### Backend (NestJS)
1. Create new modules in `src/` directory
2. Add controllers, services, and entities
3. Update API documentation
4. Write tests

#### Frontend (Next.js)
1. Create new pages in `src/app/` directory
2. Add components in `src/components/`
3. Create API services in `src/lib/api/`
4. Update state management if needed

#### Chrome Extension
1. Add new content scripts for additional marketplaces
2. Update manifest.json with new permissions
3. Enhance popup interface
4. Add background script functionality

## Building for Production

### Backend
```bash
cd tts-be-nestjs
npm run build
npm run start:prod
```

### Frontend
```bash
cd tts-fe-nextjs
npm run build
npm run start
```

### Chrome Extension
1. Create production build (if applicable)
2. Package for Chrome Web Store
3. Submit for review

## Deployment

### Backend
- Deploy to cloud platforms (AWS, Google Cloud, Azure)
- Set up PostgreSQL and Redis instances
- Configure environment variables

### Frontend
- Deploy to Vercel, Netlify, or similar platforms
- Configure environment variables
- Set up domain and SSL

### Chrome Extension
- Submit to Chrome Web Store
- Follow Chrome extension publishing guidelines
- Maintain version updates

## Contributing

1. Fork the appropriate repository
2. Create a feature branch
3. Make your changes
4. Write tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
