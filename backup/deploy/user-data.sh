#!/bin/bash
apt-get update -y
apt-get upgrade -y

# Install Node.js 22.x
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
apt-get install -y nodejs

# Install PM2 globally
npm install -g pm2

# Install Nginx
apt-get install -y nginx

# Install Git
apt-get install -y git

# Install PostgreSQL client
apt-get install -y postgresql-client

# Create application directory
mkdir -p /opt/tiktokshop
chown ubuntu:ubuntu /opt/tiktokshop

# Configure Nginx
cat > /etc/nginx/sites-available/tiktokshop << 'EOF'
server {
    listen 80;
    server_name be.tiktokshop.expert;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # Client max body size
    client_max_body_size 50M;
    
    # Proxy to NestJS app
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Health check
    location /health {
        proxy_pass http://localhost:3000/health;
        access_log off;
    }
    
    # Block Swagger API documentation in production for security
    location /api {
        return 404;
    }

    # Block any Swagger-related endpoints
    location ~* ^/(swagger|docs|api-docs) {
        return 404;
    }
}
EOF

ln -s /etc/nginx/sites-available/tiktokshop /etc/nginx/sites-enabled/
rm /etc/nginx/sites-enabled/default
nginx -t
systemctl restart nginx
systemctl enable nginx

# Install Certbot for SSL
apt-get install -y certbot python3-certbot-nginx

echo "EC2 setup completed" > /var/log/setup.log