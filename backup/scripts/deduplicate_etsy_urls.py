#!/usr/bin/env python3
"""
Script để lọc các URL Etsy trùng lặp dựa trên listing ID
"""

import csv
import re
from collections import OrderedDict

def extract_listing_id(url):
    """Trích xuất listing ID từ URL Etsy"""
    match = re.search(r'/listing/(\d+)/', url)
    return match.group(1) if match else None

def deduplicate_etsy_urls(input_file, output_file):
    """
    Lọc các URL trùng lặp dựa trên listing ID
    Giữ lại URL đầu tiên gặp phải cho mỗi listing ID
    """
    seen_listings = OrderedDict()
    duplicates = []
    
    # Đọc file CSV
    with open(input_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)  # Đọc header
        
        for row_num, row in enumerate(reader, start=2):
            if not row or not row[0].strip():  # Bỏ qua dòng trống
                continue
                
            url = row[0].strip()
            listing_id = extract_listing_id(url)
            
            if listing_id:
                if listing_id not in seen_listings:
                    seen_listings[listing_id] = {
                        'url': url,
                        'row_num': row_num
                    }
                else:
                    duplicates.append({
                        'listing_id': listing_id,
                        'duplicate_url': url,
                        'duplicate_row': row_num,
                        'original_url': seen_listings[listing_id]['url'],
                        'original_row': seen_listings[listing_id]['row_num']
                    })
            else:
                print(f"Không thể trích xuất listing ID từ URL ở dòng {row_num}: {url}")
    
    # Ghi file kết quả
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(header)  # Ghi header
        
        for listing_data in seen_listings.values():
            writer.writerow([listing_data['url']])
    
    # In thống kê
    print(f"Tổng số URL ban đầu: {len(seen_listings) + len(duplicates)}")
    print(f"Số URL duy nhất: {len(seen_listings)}")
    print(f"Số URL bị trùng: {len(duplicates)}")
    print(f"File kết quả đã được lưu: {output_file}")
    
    # In chi tiết các URL trùng lặp
    if duplicates:
        print("\nChi tiết các URL trùng lặp:")
        for dup in duplicates:
            print(f"  Listing ID {dup['listing_id']}:")
            print(f"    - Gốc (dòng {dup['original_row']}): {dup['original_url']}")
            print(f"    - Trùng (dòng {dup['duplicate_row']}): {dup['duplicate_url']}")
            print()

if __name__ == "__main__":
    input_file = "backup/etsy_listing_urls.csv"
    output_file = "backup/etsy_listing_urls_deduplicated.csv"
    
    deduplicate_etsy_urls(input_file, output_file)
