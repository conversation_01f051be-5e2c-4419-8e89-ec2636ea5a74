NODE_ENV=production
PORT=3000

DB_HOST=tiktokshop-postgres.c8j204wwyvzw.us-east-1.rds.amazonaws.com
DB_PORT=5432
DB_USERNAME=tiktokshop_admin
DB_PASSWORD=bp41?Lw3?xly1i;%
DB_DATABASE=tiktokshop
DB_SSL=true
DB_SSL_REJECT_UNAUTHORIZED=false

REDIS_HOST=tiktokshop-cluster-redis.e8zx7w.ng.0001.use1.cache.amazonaws.com
REDIS_PORT=6379

JWT_SECRET=RngivfA547JPz3VA7i5A7c19JLR4i4BEj0wWFmK0GxA=

FRONTEND_URL=https://tiktokshop.expert

EMAIL_SERVER_HOST=live.smtp.mailtrap.io
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=api
EMAIL_SERVER_PASSWORD=798b83054aa5d6227d15a9b02e7ec995
EMAIL_FROM=<EMAIL>

R2_ACCOUNT_ID=775fde0f20a10b0caf801a617944cc8a
R2_ACCESS_KEY_ID=49ebdaa67f2d74bb6a15c9221bee34ca
R2_SECRET_ACCESS_KEY=c6bc2eddb55d83fd9ff6c35377efee4fba4e64849bce67ff969883909e0ade29
R2_BUCKET_NAME=product-images
R2_PUBLIC_URL=https://media.tiktokshop.expert
R2_ENDPOINT=https://775fde0f20a10b0caf801a617944cc8a.r2.cloudflarestorage.com

GEARMENT_API_KEY=StLRFP7zXsBTzspE
GEARMENT_API_SIGNATURE=Wlspt6WmMPzr2GD0MuYNNuZyjq6HFH2F
GEARMENT_API_BASE_URL=https://api.gearment.com/v2/
