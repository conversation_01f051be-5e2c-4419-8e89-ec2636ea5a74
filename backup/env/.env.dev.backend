# Database configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=nestjsbe
DB_PASSWORD=nestjsbe123
DB_DATABASE=nestjsbedb

# Application configuration
PORT=3001
NODE_ENV=development

# Redis configuration for Bull queue
REDIS_HOST=localhost
REDIS_PORT=6379

# Temporary files directory (optional)
# If not provided, the system's temp directory will be used
# TEMP_FILES_DIR=/path/to/temp/files

# JWT Configuration
JWT_SECRET=RngivfA547JPz3VA7i5A7c19JLR4i4BEj0wWFmK0GxA=

# Email Configuration
EMAIL_SERVER_HOST=sandbox.smtp.mailtrap.io
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=e8013ba8cb5e7f
EMAIL_SERVER_PASSWORD=c72c22669bf6ac
EMAIL_FROM=<EMAIL>

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000

# Cloudflare R2 Storage configuration
# Replace these values with your actual Cloudflare R2 credentials
R2_ACCOUNT_ID=775fde0f20a10b0caf801a617944cc8a
R2_ACCESS_KEY_ID=49ebdaa67f2d74bb6a15c9221bee34ca
R2_SECRET_ACCESS_KEY=c6bc2eddb55d83fd9ff6c35377efee4fba4e64849bce67ff969883909e0ade29
R2_BUCKET_NAME=product-images
R2_PUBLIC_URL=https://media.tiktokshop.expert
R2_ENDPOINT=https://775fde0f20a10b0caf801a617944cc8a.r2.cloudflarestorage.com

GEARMENT_API_KEY=StLRFP7zXsBTzspE
GEARMENT_API_SIGNATURE=Wlspt6WmMPzr2GD0MuYNNuZyjq6HFH2F
GEARMENT_API_BASE_URL=https://api.gearment.com/v2/