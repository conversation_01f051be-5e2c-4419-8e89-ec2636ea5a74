# Debug Multiple Extraction Instances

## Problem Description

Khi extraction phase bị timeout, có 2 luồng extraction phase chạy song song:
- Luồng 1: 70 products (instance gốc)
- Luồng 2: 37 products (instance mới từ recovery)

## Root Cause Analysis

Có 2 nơi tạo ExtractionPhase instances:

1. **`transitionToNextPhase()` (line 179-185)**: <PERSON><PERSON>uyển từ search phase sang extraction phase
2. **`resumePhase()` (line 739-743)**: Khi recovery system cố gắng resume phase

### Recovery Flow Conflict

Khi extraction phase timeout:
1. `handlePhaseError()` được gọi
2. `attemptRecovery()` gọi `phase.retry()` trên instance cũ (70 products)
3. <PERSON><PERSON><PERSON> thời, recovery system có thể gọi `resumePhase()` tạo instance mới (37 products)

## Debug Code Added

### 1. Instance Tracking in ExtractionPhase

```javascript
// Generate unique instance ID for debugging
this.instanceId = `extraction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

console.log(`[ExtractionPhase] DEBUG - New instance created: ${this.instanceId}`);
console.log(`[ExtractionPhase] DEBUG - Constructor stack trace:`, new Error().stack);
```

### 2. Debug Info in Key Methods

**Constructor:**
- Logs instance ID creation
- Logs constructor stack trace

**execute():**
- Logs which instance is executing
- Logs execution stack trace

**Found 70/37 products:**
- Logs instance ID when product URLs are found
- Logs stack trace to see where it's called from

### 3. AutomationController Debug

**transitionToNextPhase():**
- Logs current phase before transition
- Logs new ExtractionPhase instance ID

**executePhase():**
- Logs executing phase instance ID
- Logs previous currentPhase instance ID

**resumePhase():**
- Logs current phase before resume
- Logs new ExtractionPhase instance ID for resume

## Fix Implemented

### 1. Recovery Flag

Added `isRecovering` flag to prevent duplicate recovery attempts:

```javascript
// In constructor
this.isRecovering = false;

// In attemptRecovery()
if (this.isRecovering) {
  console.log(`[AutomationController] DEBUG - Recovery already in progress, skipping duplicate recovery`);
  return;
}
this.isRecovering = true;

// In resumePhase()
if (this.isRecovering) {
  console.log(`[AutomationController] DEBUG - Recovery already in progress, skipping phase resume`);
  return;
}
```

### 2. Stop Existing Phase Before Resume

```javascript
// CRITICAL: Stop current phase before creating new one
if (this.currentPhase && this.currentPhase.name === phaseName) {
  console.log(`[AutomationController] DEBUG - Stopping existing ${phaseName} phase before resume`);
  if (this.currentPhase.stop) {
    await this.currentPhase.stop();
  }
  this.currentPhase.isStopped = true;
  this.currentPhase.stopRequested = true;
}
```

## Testing Instructions

1. Start crawling with low timeout (2 seconds) to trigger timeout
2. Watch console logs for:
   - Instance IDs when ExtractionPhase is created
   - Stack traces showing where instances are created
   - Recovery flag messages
   - Phase stopping messages

3. Look for patterns:
   - Should only see one "Found X product URLs to extract" message
   - Should see recovery flag preventing duplicate recovery
   - Should see existing phase being stopped before new one is created

## Expected Behavior After Fix

- Only one ExtractionPhase instance should be active at a time
- Recovery attempts should be serialized, not parallel
- Stop crawling should stop all instances properly
- No more "37 products" appearing after "70 products"
