# Debug Script Injection Process

## 🔍 **Current Issue:**

```
[SearchPhase] Script verification failed: {success: false, error: 'Script verification failed'}
```

Lỗi vẫn xảy ra sau khi đã thêm các fix. Cần debug sâu hơn để tìm nguyên nhân chính xác.

## 🛠️ **Debug Steps Added:**

### 1. **Enhanced Logging in verifyScriptsLoaded()**

```javascript
console.log(`[SearchPhase] Script verification raw result:`, result);

if (result && result[0]) {
  if (result[0].result) {
    console.log(`[SearchPhase] Script verification result:`, result[0].result);
    return result[0].result;
  } else {
    console.error(`[SearchPhase] Script verification returned null/undefined result:`, result[0]);
    return { 
      success: false, 
      error: 'Script verification returned null result',
      rawResult: result[0]
    };
  }
}
```

### 2. **Detailed Logging in verifyScriptsFunction()**

```javascript
console.log(`[SearchPhase] Starting script verification for marketplace: ${marketplace}`);

console.log(`[SearchPhase] Required scripts check:`, {
  MarketplaceNavigator: !!window.MarketplaceNavigator,
  SearchAutomation: !!window.SearchAutomation,
  CommonExtractor: !!window.CommonExtractor
});

console.log(`[SearchPhase] Week 7 scripts check:`, {
  HumanTimingSimulator: !!window.HumanTimingSimulator,
  InteractionSimulator: !!window.InteractionSimulator,
  MouseMovementSimulator: !!window.MouseMovementSimulator
});

console.log(`[SearchPhase] Script availability summary:`, { available, missing });
```

### 3. **Enhanced Test Page**

Added new test function `testStepByStep()` that:
- Checks initial page state
- Tests verification function directly
- Tests chrome.scripting.executeScript
- Checks individual script availability

## 🧪 **How to Debug:**

### **Step 1: Open Test Page**
1. Navigate to a marketplace page (Etsy/eBay/Amazon)
2. Open `tts-chrome-extension/debug/test-script-injection.html` in new tab

### **Step 2: Run Tests**
1. Click "Check Script Availability" - see what scripts are loaded
2. Click "Test Step by Step" - detailed step-by-step analysis
3. Click "Test Script Verification" - test verification function directly
4. Check browser console for detailed logs

### **Step 3: Analyze Results**
Look for:
- Which scripts are missing
- Whether verification function runs successfully
- Whether chrome.scripting.executeScript works
- Any JavaScript errors in console

## 🔍 **Expected Debug Output:**

### **If scripts are loaded correctly:**
```
Required scripts check: {
  MarketplaceNavigator: true,
  SearchAutomation: true,
  CommonExtractor: true
}
Script verification successful: {
  success: true,
  available: ["MarketplaceNavigator", "SearchAutomation", "CommonExtractor"],
  ...
}
```

### **If scripts are missing:**
```
Required scripts check: {
  MarketplaceNavigator: false,
  SearchAutomation: false,
  CommonExtractor: false
}
Missing required scripts: ["MarketplaceNavigator", "SearchAutomation", "CommonExtractor"]
```

### **If chrome.scripting.executeScript fails:**
```
Chrome scripting error: Cannot access contents of url...
```

## 🎯 **Possible Root Causes:**

### 1. **Content Scripts Not Injected by Manifest**
- Manifest content_scripts not working
- Wrong URL patterns
- Script files missing

### 2. **Chrome Extension Permissions**
- Missing scripting permission
- Missing activeTab permission
- Host permissions not granted

### 3. **Script Execution Context Issues**
- Scripts running in wrong context
- Timing issues with script loading
- Script conflicts

### 4. **Chrome.scripting.executeScript Issues**
- Function serialization problems
- Cross-context execution issues
- Tab access problems

## 🔧 **Next Steps Based on Debug Results:**

### **If scripts are missing from manifest injection:**
1. Check manifest.json content_scripts configuration
2. Verify script files exist
3. Check URL patterns match current page
4. Reload extension

### **If chrome.scripting.executeScript fails:**
1. Check extension permissions
2. Verify tab access
3. Test with simpler functions
4. Check for CSP issues

### **If verification function has errors:**
1. Simplify verification logic
2. Add more error handling
3. Test individual script checks
4. Check for JavaScript syntax errors

## 📋 **Files Modified for Debug:**

1. **search-phase.js** - Enhanced logging in verification functions
2. **test-script-injection.html** - Added step-by-step test function
3. **debug-script-injection-process.md** - This debug guide

## 🚀 **Action Plan:**

1. **Run debug tests** on marketplace page
2. **Analyze console output** to identify root cause
3. **Apply targeted fix** based on findings
4. **Verify fix** with automation test
5. **Clean up debug logging** once fixed

Với enhanced logging và debug tools này, chúng ta sẽ có thể xác định chính xác nguyên nhân của lỗi script verification và apply fix phù hợp.
