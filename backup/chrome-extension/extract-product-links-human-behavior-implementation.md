# ExtractProductLinksPhase Human Behavior Implementation

## 📋 **Problem Analysis**

### **Current Issues:**
1. **Inconsistent behavior patterns** - ExtractProductLinksPhase uses basic `waitForSearchResults()` while SearchPhase uses `waitForSearchResultsWithAdaptiveBehavior()`
2. **Missing human behavior simulation** - No behaviorConfig, applyAdaptiveTiming, or retry mechanisms
3. **Timeout failures** - Page 2+ timeouts cause complete failure, losing partial results from Page 1
4. **No graceful degradation** - Doesn't return partial results when pagination fails

### **Current Flow Issues:**
- Page 1: Extract 60 products ✅
- Page 2: Navigation success, but `waitForSearchResults()` timeout ❌
- Result: **Lose all 60 URLs** collected from Page 1 😞

## 🎯 **Solution: Apply SearchPhase Patterns**

### **Key Patterns from SearchPhase to Implement:**
1. **behaviorConfig** - Create based on maxProducts complexity
2. **applyAdaptiveTiming()** - Human-like timing adaptation
3. **findSearchInputWithRetries()** → **findNextPageWithRetries()**
4. **submitSearchWithHumanBehavior()** → **submitNextPageWithHumanBehavior()**
5. **waitForSearchResultsWithAdaptiveBehavior()** - Adaptive waiting with graceful degradation
6. **verifySearchResultsLoaded()** → **verifyNextPageLoaded()**
7. **waitForPageReady()** - Ensure page readiness before operations
8. **Retry mechanisms** with increasing delays (2s, 4s, 6s)

## 🔧 **Implementation Plan**

### **Step 1: Update ExtractProductLinksPhase Constructor**

```javascript
export class ExtractProductLinksPhase extends AutomationPhase {
  constructor(marketplace, maxProducts, config = {}) {
    super('extract-product-links', {
      marketplace,
      maxProducts,
      timeout: config.timeout || 60000,
      maxRetries: config.maxRetries || 2,
      ...config
    });

    this.marketplace = marketplace;
    this.maxProducts = maxProducts;
    this.humanTiming = new HumanTimingSimulator();
    this.behaviorEngine = new HumanBehaviorEngine(); // Add behavior engine
    this.extractedUrls = [];
    this.scrollingComplete = false;

    // Generate unique instance ID for debugging
    this.instanceId = `extract_links_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    console.log(`[ExtractProductLinksPhase] Initialized for ${marketplace}, max products: ${maxProducts}`);
  }
}
```

### **Step 2: Create behaviorConfig in execute() method**

```javascript
async execute(context) {
  console.log(`[ExtractProductLinksPhase] Starting link extraction for ${this.marketplace}`);

  // Check if automation was stopped before starting
  await this.checkStopDuringOperation('link extraction initialization');

  // Create initial checkpoint
  this.createCheckpoint({
    action: 'starting_link_extraction',
    marketplace: this.marketplace,
    maxProducts: this.maxProducts
  });

  // Create behavior configuration based on complexity
  const currentUrl = await this.getCurrentUrl(context.tabId);
  const behaviorConfig = this.behaviorEngine.adaptToPageChange(
    context.previousUrl || '',
    currentUrl,
    {
      intent: 'extract-links',
      marketplace: this.marketplace,
      complexity: this.maxProducts > 200 ? 'complex' : 'simple', // Based on maxProducts
      maxProducts: this.maxProducts
    }
  );

  // Apply adaptive timing based on behavior configuration
  await this.applyAdaptiveTiming(behaviorConfig);

  // Inject required extraction scripts
  await this.injectSearchAutomationScripts(context.tabId);

  // Extract product links with pagination and human behavior
  const productUrls = await this.extractProductLinksWithPagination(context.tabId, behaviorConfig);

  // Handle insufficient results gracefully - always continue
  if (productUrls.length === 0) {
    console.warn(`[ExtractProductLinksPhase] No product links found on search results page`);
    // Don't throw error, continue with empty array
  }

  // Validate and limit URLs
  this.extractedUrls = this.validateAndLimitUrls(productUrls);

  // Create completion checkpoint
  this.createCheckpoint({
    action: 'links_extraction_completed',
    urlCount: this.extractedUrls.length,
    urls: this.extractedUrls.slice(0, 5),
    attemptedPages: Math.min(5, Math.ceil(this.maxProducts / 20)),
    behaviorConfig: behaviorConfig.timing
  });

  console.log(`[ExtractProductLinksPhase] Completed with ${this.extractedUrls.length} URLs`);

  return {
    success: true,
    nextPhase: 'extract-product-data',
    data: {
      productUrls: this.extractedUrls,
      marketplace: this.marketplace,
      extractedCount: this.extractedUrls.length,
      maxProducts: this.maxProducts,
      behaviorConfig // Pass to next phase
    }
  };
}
```

### **Step 3: Add Human Behavior Methods**

```javascript
// Apply adaptive timing like SearchPhase
async applyAdaptiveTiming(behaviorConfig) {
  if (behaviorConfig && behaviorConfig.timing) {
    const delay = this.humanTiming.getActionPause('extract-links', {
      complexity: behaviorConfig.timing.readingTime > 300 ? 'complex' : 'simple'
    });
    await this.delay(delay);
  }
}

// Get current URL helper
async getCurrentUrl(tabId) {
  try {
    const result = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => window.location.href
    });
    return result && result[0] ? result[0].result : '';
  } catch (error) {
    console.warn(`[ExtractProductLinksPhase] Error getting current URL:`, error);
    return '';
  }
}

// Wait for page to be ready before operations
async waitForPageReady(tabId) {
  try {
    const result = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        return {
          readyState: document.readyState,
          hasProducts: document.querySelectorAll('a[href*="/listing/"], a[href*="/itm/"], a[href*="/dp/"]').length > 0,
          url: window.location.href
        };
      }
    });

    if (result && result[0] && result[0].result) {
      const pageInfo = result[0].result;
      console.log(`[ExtractProductLinksPhase] Page ready state:`, pageInfo);

      if (pageInfo.readyState !== 'complete') {
        console.log(`[ExtractProductLinksPhase] Waiting for page to complete loading...`);
        await this.delay(2000);
      }
    }
  } catch (error) {
    console.warn(`[ExtractProductLinksPhase] Error checking page ready state:`, error);
  }
}
```

### **Step 4: Enhanced Pagination Methods**

```javascript
// Find next page button with retries (like findSearchInputWithRetries)
async findNextPageWithRetries(tabId, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    console.log(`[ExtractProductLinksPhase] Finding next page button - attempt ${attempt}/${maxRetries}`);

    try {
      // Check if automation was stopped before each attempt
      await this.checkStopDuringOperation(`next page detection attempt ${attempt}`);

      // Wait for page to be fully loaded
      await this.waitForPageReady(tabId);

      const result = await this.findNextPageWithHumanScanning(tabId);

      if (result.success) {
        return result;
      }

      // Log debug information on failure
      if (result.debug) {
        console.log(`[ExtractProductLinksPhase] Debug info for attempt ${attempt}:`, result.debug);
      }

      // If not the last attempt, wait before retrying
      if (attempt < maxRetries) {
        const retryDelay = 2000 * attempt; // Increasing delay: 2s, 4s, 6s
        console.log(`[ExtractProductLinksPhase] Retrying in ${retryDelay}ms...`);

        // Check stop before delay
        await this.checkStopDuringOperation(`retry delay for attempt ${attempt + 1}`);
        await this.delay(retryDelay);
      }

    } catch (error) {
      console.error(`[ExtractProductLinksPhase] Error in attempt ${attempt}:`, error);
      if (attempt === maxRetries) {
        return { success: false, error: error.message };
      }
    }
  }

  return { success: false, error: `Failed to find next page button after ${maxRetries} attempts` };
}

// Find next page with human-like scanning
async findNextPageWithHumanScanning(tabId) {
  try {
    console.log(`[ExtractProductLinksPhase] Finding next page button with human-like scanning`);

    // Simulate human scanning behavior - look around the page first
    await this.humanTiming.getDecisionDelay();

    const result = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        return new Promise((resolve) => {
          if (window.SearchAutomation && typeof window.SearchAutomation.navigateToNextPage === 'function') {
            try {
              // Check if next page button exists (without clicking)
              const marketplace = window.SearchAutomation.detectCurrentMarketplace();
              const nextPageSelectors = {
                etsy: [
                  'a[aria-label="Next page"]',
                  '.pagination-next',
                  '.btn-pagination[data-page-direction="next"]',
                  'a[href*="page="][href*="search"]'
                ],
                ebay: [
                  'a[aria-label="Next page"]',
                  '.pagination__next',
                  '.x-pagination-next',
                  'a[href*="&_pgn="]'
                ],
                amazon: [
                  'a[aria-label="Go to next page"]',
                  '.s-pagination-next',
                  'a[href*="&page="]',
                  '.a-pagination .a-last a'
                ]
              };

              const selectors = nextPageSelectors[marketplace] || [];
              
              for (const selector of selectors) {
                const nextButton = document.querySelector(selector);
                if (nextButton && !nextButton.disabled && nextButton.href) {
                  resolve({
                    found: true,
                    selector: selector,
                    element: {
                      tagName: nextButton.tagName.toLowerCase(),
                      href: nextButton.href,
                      text: nextButton.textContent?.trim() || '',
                      disabled: nextButton.disabled
                    }
                  });
                  return;
                }
              }
              
              resolve({
                found: false,
                error: 'Next page button not found',
                marketplace: marketplace,
                url: window.location.href
              });

            } catch (error) {
              resolve({
                found: false,
                error: `SearchAutomation error: ${error.message}`,
                marketplace: window.SearchAutomation.detectCurrentMarketplace(),
                url: window.location.href
              });
            }
          } else {
            resolve({
              found: false,
              error: 'SearchAutomation not available',
              url: window.location.href
            });
          }
        });
      }
    });

    if (result && result[0] && result[0].result) {
      const nextPageResult = result[0].result;

      if (nextPageResult.found) {
        console.log(`[ExtractProductLinksPhase] Next page button found:`, nextPageResult);
        return { success: true, data: nextPageResult };
      } else {
        console.error(`[ExtractProductLinksPhase] Next page button not found:`, nextPageResult);
        return {
          success: false,
          error: nextPageResult.error || 'Next page button not found',
          debug: nextPageResult
        };
      }
    } else {
      return { success: false, error: 'No result from next page detection' };
    }

  } catch (error) {
    console.error(`[ExtractProductLinksPhase] Error finding next page button:`, error);
    return { success: false, error: error.message };
  }
}

// Submit next page with human behavior (like submitSearchWithHumanBehavior)
async submitNextPageWithHumanBehavior(tabId) {
  console.log(`[ExtractProductLinksPhase] Submitting next page with human behavior`);

  // Add human decision delay before clicking
  await this.humanTiming.getDecisionDelay();

  try {
    const result = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        return new Promise(async (resolve, reject) => {
          try {
            console.log(`[ExtractProductLinksPhase] Clicking next page button`);

            if (!window.SearchAutomation) {
              reject(new Error('SearchAutomation not available'));
              return;
            }

            // Use SearchAutomation's navigateToNextPage method
            const success = await window.SearchAutomation.navigateToNextPage();

            if (success) {
              console.log(`[ExtractProductLinksPhase] Next page navigation successful`);
              resolve({ success: true });
            } else {
              reject(new Error('Next page button not found or navigation failed'));
            }

          } catch (error) {
            console.error(`[ExtractProductLinksPhase] Error clicking next page:`, error);
            reject(error);
          }
        });
      }
    });

    if (result && result[0] && result[0].result && !result[0].result.success) {
      throw new Error(result[0].result.error);
    }

  } catch (error) {
    console.error(`[ExtractProductLinksPhase] Error submitting next page:`, error);
    throw new Error(`Failed to navigate to next page: ${error.message}`);
  }
}

// Wait for search results with adaptive behavior (like SearchPhase)
async waitForSearchResultsWithAdaptiveBehavior(tabId, behaviorConfig) {
  console.log(`[ExtractProductLinksPhase] Waiting for search results with adaptive behavior`);

  // Calculate adaptive wait time based on behavior config
  const baseWaitTime = behaviorConfig?.timing?.pageLoadWait?.[0] || 3000;
  const maxWaitTime = behaviorConfig?.timing?.pageLoadWait?.[1] || 15000; // Shorter for pagination

  const adaptiveWait = baseWaitTime + Math.random() * (maxWaitTime - baseWaitTime);

  // Add human reading time for search results
  const readingTime = this.humanTiming.getReadingTime('pagination results', {
    type: 'product-links'
  });

  await this.delay(adaptiveWait + readingTime);

  // Verify results are actually loaded
  return await this.verifyNextPageLoaded(tabId);
}

// Verify next page loaded (like verifySearchResultsLoaded)
async verifyNextPageLoaded(tabId) {
  try {
    const result = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        // Look for marketplace-specific product indicators
        const marketplace = window.SearchAutomation ? window.SearchAutomation.detectCurrentMarketplace() : 'unknown';

        const indicators = {
          etsy: [
            'a[href*="/listing/"]',
            '.listing-card',
            '[data-test-id="search-results"]'
          ],
          ebay: [
            'a[href*="/itm/"]',
            '.s-item',
            '.srp-results .s-item'
          ],
          amazon: [
            'a[href*="/dp/"]',
            '[data-component-type="s-search-result"]',
            '.s-result-item'
          ]
        };

        const selectors = indicators[marketplace] || Object.values(indicators).flat();

        for (const selector of selectors) {
          const elements = document.querySelectorAll(selector);
          if (elements.length > 0) {
            return { loaded: true, count: elements.length, selector, marketplace };
          }
        }

        return { loaded: false, count: 0, marketplace };
      }
    });

    return result && result[0] ? result[0].result : { loaded: false, count: 0 };
  } catch (error) {
    console.warn(`[ExtractProductLinksPhase] Error verifying next page results:`, error);
    return { loaded: false, count: 0 };
  }
}
```

### **Step 5: Enhanced extractProductLinksWithPagination**

```javascript
// Updated method with human behavior and graceful degradation
async extractProductLinksWithPagination(tabId, behaviorConfig) {
  try {
    console.log(`[ExtractProductLinksPhase] Extracting product links with human behavior pagination`);

    const result = await chrome.scripting.executeScript({
      target: { tabId },
      func: (marketplace, maxProducts, behaviorConfig) => {
        return new Promise(async (resolve, reject) => {
          try {
            if (!window.SearchAutomation) {
              throw new Error('SearchAutomation not available');
            }

            console.log(`[ExtractProductLinksPhase] Starting human-like pagination for ${marketplace} with max ${maxProducts} products`);

            let allProductUrls = [];
            let currentPage = 1;
            const maxPages = 5;

            while (allProductUrls.length < maxProducts && currentPage <= maxPages) {
              console.log(`[ExtractProductLinksPhase] Processing page ${currentPage}/${maxPages}`);

              // Human-like page analysis delay
              const analysisDelay = 2000 + Math.random() * 2000; // 2-4 seconds
              await window.SearchAutomation.delay(analysisDelay);

              // Scroll to load more products on current page
              await window.SearchAutomation.simulateScrolling();

              // Extract products from current page
              const pageUrls = window.SearchAutomation.extractProductLinks(maxProducts - allProductUrls.length);

              // Add new URLs (avoid duplicates)
              const newUrls = pageUrls.filter(url => !allProductUrls.includes(url));
              allProductUrls.push(...newUrls);

              console.log(`[ExtractProductLinksPhase] Page ${currentPage}: Found ${newUrls.length} new products (total: ${allProductUrls.length})`);

              // If we have enough products, stop
              if (allProductUrls.length >= maxProducts) {
                console.log(`[ExtractProductLinksPhase] Reached target of ${maxProducts} products`);
                break;
              }

              // Try to navigate to next page
              const hasNextPage = await window.SearchAutomation.navigateToNextPage();
              if (!hasNextPage) {
                console.log(`[ExtractProductLinksPhase] No more pages available`);
                break;
              }

              currentPage++;

              // Wait for next page to load with timeout handling
              try {
                await window.SearchAutomation.waitForSearchResults();
              } catch (timeoutError) {
                console.warn(`[ExtractProductLinksPhase] Page ${currentPage} load timeout, returning ${allProductUrls.length} products collected so far`);
                break; // Return partial results instead of failing
              }
            }

            console.log(`[ExtractProductLinksPhase] Pagination complete: ${allProductUrls.length} products from ${currentPage} pages`);
            resolve(allProductUrls.slice(0, maxProducts));

          } catch (error) {
            console.error(`[ExtractProductLinksPhase] Error in pagination:`, error);
            // Return partial results even on error
            resolve(allProductUrls || []);
          }
        });
      },
      args: [this.marketplace, this.maxProducts, behaviorConfig]
    });

    return result && result[0] && result[0].result ? result[0].result : [];

  } catch (error) {
    console.error(`[ExtractProductLinksPhase] Error extracting product links:`, error);
    return []; // Return empty array instead of throwing
  }
}
```

## 🎯 **Key Benefits**

1. **✅ Consistent behavior patterns** - Matches SearchPhase human-like behavior
2. **✅ Graceful degradation** - Always returns partial results, never fails completely
3. **✅ Adaptive timing** - behaviorConfig based on maxProducts complexity
4. **✅ Robust retry mechanisms** - Increasing delays (2s, 4s, 6s) for reliability
5. **✅ Human-like pagination** - Decision delays, scanning behavior, realistic timing
6. **✅ Enhanced error handling** - Timeout/error recovery without losing partial results
7. **✅ Comprehensive verification** - verifyNextPageLoaded ensures page readiness

## 🔄 **New Processing Flow**

### **ExtractProductLinksPhase Enhanced Flow:**
1. **Create behaviorConfig** → Based on maxProducts complexity (>200 = complex)
2. **Apply adaptive timing** → Human-like delays before starting
3. **Page 1**: Scroll + extract with human behavior
4. **Find next page** → findNextPageWithRetries (3 attempts with increasing delays)
5. **Navigate to next page** → submitNextPageWithHumanBehavior with decision delays
6. **Wait for page load** → waitForSearchResultsWithAdaptiveBehavior with timeout handling
7. **Verify page loaded** → verifyNextPageLoaded with marketplace-specific indicators
8. **Repeat for pages 2-5** → With graceful degradation on any failure
9. **Return results** → Always continue with partial results, never fail completely

### **Error Handling Strategy:**
- **No products found**: Continue with empty array ✅
- **Next page not found**: Return products from current pages ✅
- **Page load timeout**: Return partial results collected so far ✅
- **Navigation failure**: Return partial results collected so far ✅
- **Script injection fails**: Retry phase ✅
- **Any other error**: Return partial results, log error, continue workflow ✅

## 📝 **Implementation Notes**

### **behaviorConfig Structure:**
```javascript
{
  intent: 'extract-links',
  marketplace: 'etsy',
  complexity: 'complex', // Based on maxProducts > 200
  maxProducts: 250,
  timing: {
    pageLoadWait: [3000, 15000], // Shorter for pagination
    readingTime: 2000,
    decisionDelay: [800, 3500]
  }
}
```

### **Human Behavior Patterns:**
- **Page analysis delays**: 2-4 seconds before extracting
- **Decision delays**: Before clicking next page
- **Scanning behavior**: Look around page before finding next button
- **Realistic waiting**: Adaptive timeouts based on complexity
- **Retry patterns**: Increasing delays matching SearchPhase

### **Graceful Degradation Examples:**
- Page 1: 60 products, Page 2: timeout → Return 60 products ✅
- Page 1-3: 150 products, Page 4: error → Return 150 products ✅
- Page 1-5: 200 products, no more pages → Return 200 products ✅
```
