# Chrome Extension Repository Setup Guide

This guide explains how to organize the Chrome Extension as a separate repository, following the same pattern as the backend and frontend repositories.

## Overview

The TikTok Shop project consists of three main components:
1. **Backend (NestJS)**: `tts-be-nestjs` repository
2. **Frontend (Next.js)**: `tts-fe-nextjs` repository  
3. **Chrome Extension**: `tts-chrome-extension` repository (new)

## Step-by-Step Setup

### 1. Prepare the Chrome Extension Repository

Run the setup script from the main project directory:

```bash
./setup-chrome-extension-repo.sh
```

This script will:
- Copy all files from `chrome-extension-product-crawler/` to `tts-chrome-extension/`
- Create necessary repository files (`package.json`, `.gitignore`, `LICENSE`)
- Initialize a git repository with an initial commit
- Provide next steps for GitHub setup

### 2. Create GitHub Repository

1. Go to [GitHub](https://github.com) and create a new repository
2. Repository name: `tts-chrome-extension`
3. Description: "Chrome extension for extracting product information from e-commerce marketplaces and integrating with TikTok Shop"
4. Set as Public or Private (your choice)
5. **Do not** initialize with README, .gitignore, or license (already created by script)

### 3. Connect Local Repository to GitHub

```bash
cd tts-chrome-extension

# Add remote origin
git remote add origin https://github.com/vinhWater/tts-chrome-extension.git

# Push to GitHub
git branch -M main
git push -u origin main
```

### 4. Update Main Project Structure

Remove the old directory and clone the new repository:

```bash
cd ..  # Back to main project directory

# Remove old chrome extension directory
rm -rf chrome-extension-product-crawler

# Clone the new repository
git clone https://github.com/vinhWater/tts-chrome-extension.git

# Update main project git to ignore the cloned repository
echo "tts-chrome-extension/" >> .gitignore
```

### 5. Update Main Project Documentation

The main README.md has already been updated to include the Chrome extension. Commit these changes:

```bash
git add README.md docs/chrome-extension-repository-setup.md setup-chrome-extension-repo.sh
git commit -m "Add Chrome extension as separate repository

- Updated README.md to include Chrome extension information
- Added setup guide for Chrome extension repository
- Created setup script for repository organization"
git push origin main
```

## Repository Structure After Setup

```
tiktokshop/                          # Main repository
├── README.md                        # Updated with Chrome extension info
├── docs/
│   └── chrome-extension-repository-setup.md
├── setup-chrome-extension-repo.sh   # Setup script
├── tts-be-nestjs/                   # Backend repository (git clone)
├── tts-fe-nextjs/                   # Frontend repository (git clone)
└── tts-chrome-extension/            # Chrome extension repository (git clone)
```

## Development Workflow

### Working with the Chrome Extension

1. **Navigate to the extension directory**:
   ```bash
   cd tts-chrome-extension
   ```

2. **Make your changes** to the extension files

3. **Test the extension**:
   - Load unpacked extension in Chrome
   - Test functionality on supported marketplaces
   - Verify API integration with backend

4. **Commit and push changes**:
   ```bash
   git add .
   git commit -m "Add new feature or fix"
   git push origin main
   ```

### Keeping Repositories in Sync

Since the repositories are separate, you'll need to manage them individually:

1. **Backend changes**: Work in `tts-be-nestjs/`
2. **Frontend changes**: Work in `tts-fe-nextjs/`
3. **Extension changes**: Work in `tts-chrome-extension/`
4. **Documentation updates**: Work in main `tiktokshop/` repository

## Benefits of Separate Repository

### Advantages
- **Independent versioning**: Each component can have its own release cycle
- **Focused development**: Developers can work on specific components
- **Cleaner history**: Git history is focused on relevant changes
- **Easier deployment**: Each component can be deployed independently
- **Better organization**: Clear separation of concerns

### Considerations
- **Multiple repositories**: Need to manage three separate repositories
- **Coordination**: Changes affecting multiple components require coordination
- **Documentation**: Keep main repository documentation updated

## Chrome Extension Specific Setup

### Development Environment

1. **Load extension in Chrome**:
   ```
   chrome://extensions/ → Developer mode → Load unpacked → Select tts-chrome-extension/
   ```

2. **Backend API**: Ensure backend is running on `http://localhost:3001`

3. **Frontend**: Ensure frontend is running on `http://localhost:3000`

### Extension Files Structure

```
tts-chrome-extension/
├── manifest.json              # Extension configuration
├── background/               # Background scripts
│   ├── service-worker.js    # Main service worker
│   ├── api-client.js        # API communication
│   └── scheduler.js         # Automated crawling
├── content-scripts/         # Marketplace extractors
│   ├── common-extractor.js  # Shared utilities
│   ├── etsy-extractor.js    # Etsy-specific extraction
│   ├── ebay-extractor.js    # eBay-specific extraction
│   └── amazon-extractor.js  # Amazon-specific extraction
├── popup/                   # Extension popup
│   ├── popup.html          # Popup interface
│   ├── popup.css           # Popup styling
│   └── popup.js            # Popup functionality
├── icons/                   # Extension icons
├── package.json            # Repository metadata
├── README.md               # Extension documentation
├── .gitignore             # Git ignore rules
└── LICENSE                # MIT license
```

## Next Steps

1. **Complete the repository setup** using this guide
2. **Test the extension** in the new repository structure
3. **Update any documentation** that references the old directory structure
4. **Inform team members** about the new repository organization
5. **Set up CI/CD** for the Chrome extension if needed

## Troubleshooting

### Common Issues

1. **Permission denied when running script**:
   ```bash
   chmod +x setup-chrome-extension-repo.sh
   ```

2. **Repository already exists on GitHub**:
   - Delete the existing repository or use a different name
   - Update the script with the new repository URL

3. **Extension not loading in Chrome**:
   - Check manifest.json syntax
   - Verify all file paths are correct
   - Check Chrome developer console for errors

### Getting Help

- Check the main project documentation
- Review Chrome extension development guides
- Consult the team for repository-specific questions
