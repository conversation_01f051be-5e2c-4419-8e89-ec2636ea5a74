# Fix Extraction Phase Retry Logic Issue

## Problem Summary

### Issue Description
When extraction phase times out, it appears to create 2 concurrent extraction flows with different product counts:
- Flow 1: 70 products
- Flow 2: 37 products

### Root Cause Analysis
**NOT multiple instances** - This is the **same instance** with incorrect retry logic:

1. **Execute attempt 1:**
   - ✅ `extractProductLinksWithScrolling()` → 70 products from search results page
   - ✅ Navigate to product detail page
   - ❌ `extractProductDataWithValidation()` → **TIMEOUT**

2. **Execute attempt 2 (Retry):**
   - ❌ `retry()` restarts entire `execute()` method from beginning
   - ❌ `extractProductLinksWithScrolling()` runs again on **product detail page**
   - ❌ Extracts related products → 37 products instead of original 70

### Technical Root Cause
```javascript
// automation-phase.js line 207
async retry(context = {}) {
  return await this.start({ ...this.context, ...context, isRetry: true });
}
```
**`retry()` restarts entire phase instead of resuming from failure point!**

## Solution: Granular Retry (Option 2)

### Step 1: Add Operation Tracking

**File:** `tts-chrome-extension/background/phases/extraction-phase.js`

```javascript
constructor(marketplace, maxProducts = 200) {
  // ... existing code ...
  
  // Add operation tracking
  this.currentOperation = null;
  this.currentProductIndex = 0;
  this.operationData = {};
}
```

### Step 2: Track Operations in Execute Method

```javascript
async execute(context) {
  // Phase 1: Extract product links (run only once)
  if (!this.productUrls || this.productUrls.length === 0) {
    this.currentOperation = 'extracting_product_links';
    this.productUrls = await this.extractProductLinksWithScrolling(context.tabId);
  }
  
  // Phase 2: Extract each product (resume from currentProductIndex)
  for (let i = this.currentProductIndex; i < this.productUrls.length; i++) {
    this.currentProductIndex = i;
    this.currentOperation = 'navigating_to_product';
    this.operationData = { productUrl: this.productUrls[i], index: i };
    
    await this.navigateToProductWithHumanBehavior(context.tabId, this.productUrls[i]);
    
    this.currentOperation = 'extracting_product_data';
    const productData = await this.extractProductDataWithValidation(context.tabId);
    
    if (productData && productData.success) {
      this.currentOperation = 'saving_product';
      this.operationData.productData = productData.data;
      await this.saveProductToBackend(productData.data);
    }
    
    this.currentOperation = null;
  }
}
```

### Step 3: Implement Granular Retry Method

```javascript
async retryCurrentOperation(context) {
  console.log(`[ExtractionPhase] Retrying operation: ${this.currentOperation}`);
  
  switch (this.currentOperation) {
    case 'extracting_product_links':
      return await this.extractProductLinksWithScrolling(context.tabId);
      
    case 'navigating_to_product':
      const { productUrl } = this.operationData;
      return await this.navigateToProductWithHumanBehavior(context.tabId, productUrl);
      
    case 'extracting_product_data':
      return await this.extractProductDataWithValidation(context.tabId);
      
    case 'saving_product':
      const { productData } = this.operationData;
      return await this.saveProductToBackend(productData);
      
    default:
      throw new Error(`Cannot retry unknown operation: ${this.currentOperation}`);
  }
}
```

### Step 4: Override Retry Method

```javascript
async retry(context = {}) {
  if (this.retryCount >= this.maxRetries) {
    throw new Error(`Phase ${this.name} exceeded maximum retries (${this.maxRetries})`);
  }
  
  this.retryCount++;
  console.log(`[AutomationPhase:${this.name}] Retry attempt ${this.retryCount}/${this.maxRetries}`);
  
  // Granular retry: only retry current operation
  if (this.currentOperation) {
    console.log(`[ExtractionPhase] Granular retry for operation: ${this.currentOperation}`);
    return await this.retryCurrentOperation(context);
  } else {
    // Fallback: restart entire phase if no specific operation
    console.log(`[ExtractionPhase] Full restart retry`);
    return await this.start({ ...this.context, ...context, isRetry: true });
  }
}
```

### Step 5: Enhanced Error Handling

```javascript
// In try-catch blocks of execute()
catch (error) {
  console.error(`[ExtractionPhase] Error in operation ${this.currentOperation}:`, error);
  
  // Store operation data for retry
  if (this.currentOperation === 'extracting_product_data' && productData) {
    this.operationData.productData = productData;
  }
  
  throw error; // Let automation-controller handle retry
}
```

## Expected Results

### Before Fix:
- Timeout → Retry entire phase → Re-extract product links → 37 products

### After Fix:
- Timeout → Retry only failed operation → Keep original 70 products → Resume from current product

## Files to Modify

1. **`tts-chrome-extension/background/phases/extraction-phase.js`**
   - Add operation tracking properties
   - Implement granular retry logic
   - Override retry method
   - Add operation state management

## Test Cases

1. **Test timeout during product data extraction**
   - Verify no re-extraction of product links
   - Verify resume from correct product index

2. **Test timeout during navigation**
   - Verify retry navigates to same product
   - Verify product list remains unchanged

3. **Test timeout during save operation**
   - Verify retry attempts to save same product data
   - Verify no duplicate extractions

## Implementation Notes

- Preserve existing checkpoint and progress tracking
- Maintain backward compatibility with current retry system
- Add detailed logging for debugging operation states
- Ensure proper cleanup of operation state after completion
