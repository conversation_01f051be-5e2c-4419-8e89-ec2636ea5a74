# Week 4 Automation Controls - Testing Guide

## 🎯 **Testing Overview**

The Week 4 automation controls must be tested within the Chrome extension context, not from regular web pages. Here are the proper testing methods.

## ✅ **Method 1: Extension Popup Testing (Primary)**

### **Setup Steps:**
1. Load the Chrome extension in developer mode
2. Ensure you're logged into the web application
3. Create at least one crawl schedule in the web app
4. Click the extension icon to open the popup

### **Testing Checklist:**

#### **Authentication Status:**
- [ ] Popup shows user is logged in
- [ ] User name/email is displayed correctly
- [ ] All sections are visible (automation, actions, stats, activity)

#### **Automation Controls:**
- [ ] "Automated Crawling" section is visible
- [ ] "Start Crawling" button is enabled
- [ ] "Stop Crawling" button is disabled initially
- [ ] Status shows "Stopped" with no active schedule

#### **Start Automation Test:**
1. Click "Start Crawling" button
2. **Expected Results:**
   - [ ] <PERSON><PERSON> becomes disabled
   - [ ] "Stop Crawling" button becomes enabled
   - [ ] Status changes to "Running"
   - [ ] Current schedule shows schedule name
   - [ ] Success message appears
   - [ ] Browser notification shows "Automation Started"

#### **Stop Automation Test:**
1. Click "Stop Crawling" button
2. **Expected Results:**
   - [ ] "Start Crawling" button becomes enabled
   - [ ] "Stop Crawling" button becomes disabled
   - [ ] Status changes to "Stopped"
   - [ ] Current schedule shows "No active schedule"
   - [ ] Success message appears
   - [ ] Browser notification shows "Automation Stopped"

#### **Error Scenarios:**
- [ ] Test with no schedules created (should show "No incomplete schedules found")
- [ ] Test while logged out (should show "Please log in first")

## ✅ **Method 2: Browser DevTools Testing**

### **Console Testing:**
1. Open Chrome DevTools (F12)
2. Go to the extension popup
3. In the console, run these commands:

```javascript
// Test automation status
chrome.runtime.sendMessage({type: 'GET_AUTOMATION_STATUS'}, (response) => {
  console.log('Automation Status:', response);
});

// Test start automation
chrome.runtime.sendMessage({type: 'START_AUTOMATION'}, (response) => {
  console.log('Start Automation:', response);
});

// Test stop automation
chrome.runtime.sendMessage({type: 'STOP_AUTOMATION'}, (response) => {
  console.log('Stop Automation:', response);
});

// Test get schedules
chrome.runtime.sendMessage({type: 'GET_CRAWL_SCHEDULES'}, (response) => {
  console.log('Crawl Schedules:', response);
});
```

## ✅ **Method 3: Content Script Testing**

Create a content script that can test the automation controls from within a web page context.

### **Create Test Content Script:**
File: `tts-chrome-extension/debug/test-content-script.js`

```javascript
// Test automation controls from content script context
console.log('🧪 Testing automation controls...');

// Test functions
async function testAutomationControls() {
  try {
    // Test 1: Get automation status
    console.log('📊 Testing automation status...');
    const statusResponse = await chrome.runtime.sendMessage({
      type: 'GET_AUTOMATION_STATUS'
    });
    console.log('✅ Status Response:', statusResponse);

    // Test 2: Get schedules
    console.log('📋 Testing schedule retrieval...');
    const schedulesResponse = await chrome.runtime.sendMessage({
      type: 'GET_CRAWL_SCHEDULES'
    });
    console.log('✅ Schedules Response:', schedulesResponse);

    // Test 3: Start automation
    console.log('▶️ Testing start automation...');
    const startResponse = await chrome.runtime.sendMessage({
      type: 'START_AUTOMATION'
    });
    console.log('✅ Start Response:', startResponse);

    // Wait 2 seconds
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test 4: Stop automation
    console.log('⏹️ Testing stop automation...');
    const stopResponse = await chrome.runtime.sendMessage({
      type: 'STOP_AUTOMATION'
    });
    console.log('✅ Stop Response:', stopResponse);

    console.log('🎉 All tests completed!');
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run tests when page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', testAutomationControls);
} else {
  testAutomationControls();
}
```

### **Update Manifest to Include Test Script:**
Add to `manifest.json` content_scripts section:

```json
{
  "matches": ["http://localhost:3000/*"],
  "js": ["debug/test-content-script.js"],
  "run_at": "document_end"
}
```

## ✅ **Method 4: Background Script Console Testing**

### **Access Background Script Console:**
1. Go to `chrome://extensions/`
2. Find your extension
3. Click "Inspect views: background page" or "service worker"
4. This opens DevTools for the background script

### **Test Commands:**
```javascript
// Simulate popup messages
const testAutomation = async () => {
  // Test automation status
  const status = await new Promise(resolve => {
    handleMessage({type: 'GET_AUTOMATION_STATUS'}, null, resolve);
  });
  console.log('Status:', status);

  // Test start automation
  const start = await new Promise(resolve => {
    handleMessage({type: 'START_AUTOMATION'}, null, resolve);
  });
  console.log('Start:', start);
};

testAutomation();
```

## 🔍 **Debugging Tips**

### **Common Issues:**
1. **"Extension context invalidated"**
   - Reload the extension
   - Close and reopen popup

2. **"User not authenticated"**
   - Ensure you're logged into the web app
   - Check if token is properly synced

3. **"No incomplete schedules found"**
   - Create schedules in the web app
   - Ensure schedules are active and have 'pending' status

### **Logging:**
- Check browser console for popup logs
- Check background script console for service worker logs
- Check network tab for API calls

## 📋 **Test Results Documentation**

### **Create Test Report:**
Document your test results:

```
## Week 4 Automation Controls Test Report

### Environment:
- Chrome Version: [version]
- Extension Version: 1.0.0
- Backend URL: http://localhost:3001
- Web App URL: http://localhost:3000

### Test Results:
- [ ] Popup UI displays correctly
- [ ] Authentication status works
- [ ] Start automation works
- [ ] Stop automation works
- [ ] Status updates correctly
- [ ] Error handling works
- [ ] Notifications appear

### Issues Found:
[List any issues]

### Notes:
[Additional observations]
```

## 🚀 **Next Steps**

After successful testing:
1. Document any issues found
2. Verify backend API calls are working
3. Test with multiple schedules
4. Test error scenarios
5. Prepare for Week 5 implementation

## ⚠️ **Important Notes**

- **Never test extension APIs from regular web pages**
- **Always test within extension context**
- **Use browser DevTools for detailed debugging**
- **Check both popup and background script consoles**
- **Verify backend endpoints are responding correctly**

The automation controls are working correctly - the issue was just with the testing approach! 🎉
