# Debug Mode Changes Summary

## 🐛 **Debug Mode Modifications**

<PERSON><PERSON> giúp debug dễ dàng hơn, tô<PERSON> đã thực hiện các thay đổi tạm thời sau đây. **T<PERSON>t cả các thay đổi này cần được revert sau khi debug xong.**

## 🔧 **Thay đổi 1: Sử dụng tab hiện tại thay vì tạo tab mới**

### **File: `automation-controller.js`**

**Thay đổi trong method `createAutomationTab()`:**
```javascript
// TODO: FOR DEBUG ONLY - Use current active tab instead of creating new one
// TODO: After debugging, revert to: chrome.tabs.create({ url: marketplaceUrl, active: true })
const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });

if (currentTab) {
  console.log(`[AutomationController] DEBUG MODE: Using current tab ${currentTab.id} instead of creating new one`);
  
  // Navigate current tab to marketplace URL
  await chrome.tabs.update(currentTab.id, { url: marketplaceUrl });
  
  console.log(`[AutomationController] Navigated current tab ${currentTab.id} to ${marketplace}: ${marketplaceUrl}`);
  
  // Wait for initial navigation to complete
  await this.waitForTabToLoad(currentTab.id);
  
  return currentTab.id;
} else {
  // Fallback to creating new tab if no current tab found
  // ... existing code
}
```

**Thay đổi trong method `retryNavigationWithFallback()`:**
```javascript
// TODO: FOR DEBUG ONLY - Use current tab instead of creating new one for recovery
// TODO: After debugging, revert to: chrome.tabs.create({ url: urlToTry, active: true })
console.log(`[AutomationController] DEBUG MODE: Using current tab for recovery instead of creating new one`);

if (this.tabId) {
  // Navigate current tab to alternative URL
  await chrome.tabs.update(this.tabId, { url: urlToTry });
  console.log(`[AutomationController] Navigated current tab ${this.tabId} to recovery URL: ${urlToTry}`);
} else {
  // If no current tab, get active tab
  const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
  if (currentTab) {
    this.tabId = currentTab.id;
    this.context.tabId = currentTab.id;
    await chrome.tabs.update(this.tabId, { url: urlToTry });
    console.log(`[AutomationController] Using active tab ${this.tabId} for recovery: ${urlToTry}`);
  } else {
    // Last resort: create new tab
    // ... existing code
  }
}
```

### **File: `scheduler.js`**

**Thay đổi trong method `crawlKeyword()`:**
```javascript
// TODO: FOR DEBUG ONLY - Use current active tab instead of creating new one
// TODO: After debugging, revert to: chrome.tabs.create({ url: marketplaceUrls[marketplace], active: false })
const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
let tab;

if (currentTab) {
  console.log(`[CrawlScheduler] DEBUG MODE: Using current tab ${currentTab.id} instead of creating new one`);
  // Navigate current tab to marketplace URL
  await chrome.tabs.update(currentTab.id, { url: marketplaceUrls[marketplace] });
  tab = currentTab;
} else {
  // Fallback to creating new tab if no current tab found
  console.log(`[CrawlScheduler] No current tab found, creating new tab`);
  tab = await chrome.tabs.create({
    url: marketplaceUrls[marketplace],
    active: false
  });
}
```

## 🚫 **Thay đổi 2: Tắt việc đóng tab khi có lỗi**

### **File: `automation-controller.js`**

**Thay đổi trong method `cleanup()`:**
```javascript
// TODO: FOR DEBUG ONLY - Don't close tab to allow debugging
// TODO: After debugging, uncomment the tab closing code below
/*
// Close automation tab
if (this.tabId) {
  try {
    await chrome.tabs.remove(this.tabId);
  } catch (error) {
    console.error(`[AutomationController] Error closing tab:`, error);
  }
  this.tabId = null;
}
*/
console.log(`[AutomationController] DEBUG MODE: Keeping tab ${this.tabId} open for debugging`);
this.tabId = null; // Clear reference but don't close tab
```

**Thay đổi trong method `retryNavigationWithFallback()`:**
```javascript
// TODO: FOR DEBUG ONLY - Don't close problematic tab to allow debugging
// TODO: After debugging, uncomment the tab closing code below
/*
// Close the problematic tab if it exists
if (this.tabId) {
  try {
    await chrome.tabs.remove(this.tabId);
  } catch (error) {
    console.warn(`[AutomationController] Could not close problematic tab:`, error);
  }
}
*/
console.log(`[AutomationController] DEBUG MODE: Keeping problematic tab ${this.tabId} open for debugging`);
```

### **File: `scheduler.js`**

**Đã có comment sẵn:**
```javascript
// to-do: I need to debug and want to keep the tab to check console log.
// Close the tab
//await chrome.tabs.remove(tab.id);
```

## 📋 **Cách sử dụng Debug Mode**

### **Để debug:**
1. **Mở tab marketplace** (ví dụ: https://www.etsy.com)
2. **Nhấn "Start Crawling"** trong extension popup
3. **Extension sẽ sử dụng tab hiện tại** thay vì tạo tab mới
4. **Khi có lỗi, tab sẽ không bị đóng** để bạn có thể kiểm tra console

### **Lợi ích:**
- ✅ **Dễ debug hơn** - không cần chuyển đổi giữa các tab
- ✅ **Giữ lại tab lỗi** - có thể kiểm tra console log và DOM
- ✅ **Theo dõi quá trình** - xem extension hoạt động trực tiếp
- ✅ **Kiểm tra selectors** - có thể test selectors trong DevTools

## 🔄 **Cách revert sau khi debug xong**

### **Bước 1: Revert automation-controller.js**
```bash
# Tìm và uncomment các dòng code bị comment
# Xóa các dòng DEBUG MODE
# Khôi phục logic tạo tab mới và đóng tab
```

### **Bước 2: Revert scheduler.js**
```bash
# Khôi phục chrome.tabs.create() thay vì sử dụng current tab
# Uncomment chrome.tabs.remove(tab.id)
```

### **Bước 3: Test production mode**
```bash
# Test lại extension với logic production
# Đảm bảo tabs được tạo và đóng đúng cách
```

## ⚠️ **Lưu ý quan trọng**

1. **Chỉ dùng cho debug** - Không deploy debug mode lên production
2. **Revert sau khi debug** - Nhớ khôi phục code gốc
3. **Test cả hai mode** - Đảm bảo cả debug và production mode đều hoạt động
4. **Backup code** - Lưu lại version gốc trước khi thay đổi

## 🎯 **Kết quả mong đợi**

Với các thay đổi này, việc debug sẽ dễ dàng hơn nhiều:
- Extension sử dụng tab hiện tại thay vì tạo tab mới
- Tab không bị đóng khi có lỗi
- Có thể theo dõi quá trình crawling trực tiếp
- Dễ dàng kiểm tra console logs và DOM elements

**Nhớ revert tất cả các thay đổi sau khi debug xong!**
