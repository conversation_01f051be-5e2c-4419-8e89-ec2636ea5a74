# ExtractProductDataPhase Timeout Handling Implementation

## Overview

This document describes the implementation of timeout handling for the ExtractProductDataPhase that prevents infinite recovery loops and properly marks crawl schedules as failed when phase-level timeouts occur.

## Problem Statement

Previously, when ExtractProductDataPhase timed out (after 4 hours), it would enter the recovery mechanism which would attempt to resume/retry the phase, potentially creating an infinite loop that never ends the crawl schedule.

## Solution Architecture

### 1. Timeout Detection

The system now distinguishes between two types of timeouts:

- **Phase-level timeout**: When the entire ExtractProductDataPhase exceeds its configured timeout (4 hours)
- **Product-level timeout**: When an individual product extraction exceeds its timeout (3 minutes)

### 2. Phase-Level Timeout Handling

When a phase-level timeout is detected:

1. **Immediate Stop**: The phase is stopped immediately without attempting recovery
2. **Schedule Update**: The crawl schedule is marked as 'failed' with timeout reason
3. **State Transition**: The automation transitions to 'TIMEOUT_FAILED' state
4. **No Recovery**: The recovery mechanism is bypassed for timeout errors

### 3. Product-Level Timeout Handling

When a product-level timeout occurs:

1. **Skip Product**: The individual product is skipped and added to skipped list
2. **Continue Phase**: The phase continues with the next product
3. **No Phase Stop**: The overall phase execution is not interrupted

## Implementation Details

### AutomationController Changes

#### New Method: `shouldStopOnTimeout(phase, structuredError)`
```javascript
shouldStopOnTimeout(phase, structuredError) {
  // Only stop on timeout for ExtractProductDataPhase
  if (structuredError.phaseName !== 'extract-product-data') {
    return false;
  }

  // Check if this is a phase-level timeout
  const isPhaseTimeout = structuredError.errorType === 'timeout' && 
                        structuredError.message.includes(`Phase ${phase.name} timed out after`);

  return isPhaseTimeout;
}
```

#### New Method: `handlePhaseTimeout(phase, structuredError)`
```javascript
async handlePhaseTimeout(phase, structuredError) {
  // Stop the current phase immediately
  if (phase && phase.stop) {
    await phase.stop();
  }

  // Update schedule status to failed with timeout reason
  if (this.context.schedule) {
    const timeoutMessage = `Phase ${structuredError.phaseName} timed out after ${phase.config?.timeout || 'unknown'}ms`;
    
    await this.apiClient.updateScheduleStatus(
      this.context.schedule.id,
      'failed',
      phase.extractedProducts?.length || 0,
      timeoutMessage
    );
  }

  // Transition to TIMEOUT_FAILED state
  this.stateMachine.transition('TIMEOUT_FAILED', {
    phase: structuredError.phaseName,
    error: structuredError.message,
    reason: 'Phase timeout exceeded',
    timestamp: Date.now()
  });

  // Cleanup without attempting recovery
  await this.cleanup();
}
```

#### Modified: `handlePhaseError(phase, error)`
Added timeout detection before recovery attempts:
```javascript
// Check if this is a phase-level timeout for ExtractProductDataPhase
if (this.shouldStopOnTimeout(phase, structuredError)) {
  await this.handlePhaseTimeout(phase, structuredError);
  return; // Exit without attempting recovery
}
```

#### Modified: `canRecoverFromError(phase, structuredError)`
Prevents recovery for phase-level timeouts:
```javascript
// Never attempt recovery for phase-level timeouts in ExtractProductDataPhase
if (this.shouldStopOnTimeout(phase, structuredError)) {
  return false;
}
```

### NavigationStateMachine Changes

#### New State: `TIMEOUT_FAILED`
Added to state transitions and utility methods:
```javascript
'TIMEOUT_FAILED': ['IDLE'] // TIMEOUT_FAILED can only go back to IDLE

isTimeoutFailed() {
  return this.currentState === 'TIMEOUT_FAILED';
}
```

### ExtractProductDataPhase Changes

#### New Method: `stop()`
Properly handles phase stopping:
```javascript
async stop() {
  // Set stop flags
  this.isStopped = true;
  this.stopRequested = true;
  
  // Update status
  this.status = 'stopped';
  this.endTime = Date.now();
  
  // Clear current operation
  this.currentOperation = null;
  this.operationData = {};
  
  return {
    success: true,
    stopped: true,
    extractedCount: this.extractedProducts.length,
    totalProducts: this.productUrls.length,
    reason: 'Phase stopped due to timeout or user request'
  };
}
```

#### Modified: `processProduct(index, context)`
Individual product failures no longer throw errors that would trigger phase-level recovery:
```javascript
if (this.productRetryCount > this.config.maxProductRetries) {
  // Log as failed but don't throw - continue with next product
  this.failedProducts.push({...});
  return; // Continue with next product instead of throwing
}
```

#### Modified: `execute(context)`
Added error handling to continue with next products when individual products fail:
```javascript
for (let i = this.currentProductIndex; i < this.productUrls.length; i++) {
  try {
    await this.processProduct(i, context);
  } catch (error) {
    // Handle stop errors vs other errors
    if (error.message && error.message.includes('stopped by user')) {
      break; // Stop processing more products
    }
    
    // For other errors, log and continue with next product
    this.failedProducts.push({...});
    continue;
  }
}
```

## Configuration

The timeout handling uses the following configuration:

- **Phase Timeout**: 14400000ms (4 hours) - configured in ExtractProductDataPhase constructor
- **Product Timeout**: 180000ms (3 minutes) - configured per product
- **Max Product Retries**: 2 - number of retries per product before skipping

## Testing

A test file has been created at `debug/test-timeout-handling.html` to verify:

1. Phase-level timeout detection and handling
2. Product-level timeout handling (should continue)
3. Normal execution flow
4. State transitions and schedule updates

## Benefits

1. **No Infinite Loops**: Phase-level timeouts immediately stop the automation
2. **Clear Status**: Crawl schedules are properly marked as failed with timeout reason
3. **Resource Management**: Long-running phases don't consume resources indefinitely
4. **Graceful Degradation**: Individual product failures don't stop the entire phase
5. **Better Monitoring**: Clear distinction between timeout types for debugging

## Usage

The timeout handling is automatic and requires no additional configuration. The system will:

1. Continue processing products when individual products timeout
2. Stop the entire automation when the phase-level timeout is reached
3. Update the crawl schedule status appropriately
4. Transition to the correct state for proper cleanup

This ensures that ExtractProductDataPhase operations have a definitive end point and don't run indefinitely due to recovery loops.
