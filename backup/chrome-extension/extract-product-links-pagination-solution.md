# ExtractProductLinksPhase Pagination Implementation Solution

## 📋 **Problem Analysis**

### **Current Flow:**
1. **SearchPhase** → Search and wait for results
2. **ExtractProductLinksPhase** → Extract links with scrolling only
3. **ExtractProductDataPhase** → Extract data from each product

### **Current Issues:**
- `ExtractProductLinksPhase` only scrolls within current page
- No pagination logic to click "Next Page" buttons
- No retry mechanism when insufficient products found (`< maxProducts`)
- Fails to utilize multiple pages of search results

## 🎯 **Proposed Solution**

### **Requirements:**
- When `ExtractProductLinksPhase` finds insufficient product URLs (`< maxProducts`)
- Simulate human clicking on "Next Page" button
- Continue extracting from subsequent pages
- Retry up to 5 pages maximum
- If still insufficient after 5 pages, proceed to `ExtractProductDataPhase` anyway
- Maintain transparent error handling flow across SearchPhase and NavigationPhase

## 🔧 **Implementation Plan**

### **1. Add Pagination Logic to SearchAutomation**

**File:** `tts-chrome-extension/content-scripts/search-automation.js`

**New Methods to Add:**

```javascript
// Navigate to next page with marketplace-specific selectors
async navigateToNextPage() {
  const marketplace = this.detectCurrentMarketplace();
  
  const nextPageSelectors = {
    etsy: [
      'a[aria-label="Next page"]',
      '.pagination-next',
      '.btn-pagination[data-page-direction="next"]',
      'a[href*="page="][href*="search"]'
    ],
    ebay: [
      'a[aria-label="Next page"]',
      '.pagination__next',
      '.x-pagination-next',
      'a[href*="&_pgn="]'
    ],
    amazon: [
      'a[aria-label="Go to next page"]',
      '.s-pagination-next',
      'a[href*="&page="]',
      '.a-pagination .a-last a'
    ]
  };

  const selectors = nextPageSelectors[marketplace] || [];
  
  for (const selector of selectors) {
    const nextButton = document.querySelector(selector);
    if (nextButton && !nextButton.disabled && nextButton.href) {
      this.log(`Found next page button: ${selector}`);
      await this.simulateClick(nextButton);
      return true;
    }
  }
  
  this.log('No next page button found');
  return false;
}

// Extract product links with pagination support
async extractProductLinksWithPagination(maxProducts, maxPages = 5) {
  this.log(`Extracting product links with pagination (max: ${maxProducts}, maxPages: ${maxPages})`);
  
  let allProductUrls = [];
  let currentPage = 1;
  
  while (allProductUrls.length < maxProducts && currentPage <= maxPages) {
    this.log(`Processing page ${currentPage}/${maxPages}`);
    
    // Wait for page to load
    await this.delay(this.randomDelay(2000, 4000));
    
    // Scroll to load more products on current page
    await this.simulateScrolling();
    
    // Extract products from current page
    const pageUrls = this.extractProductLinks(maxProducts - allProductUrls.length);
    
    // Add new URLs (avoid duplicates)
    const newUrls = pageUrls.filter(url => !allProductUrls.includes(url));
    allProductUrls.push(...newUrls);
    
    this.log(`Page ${currentPage}: Found ${newUrls.length} new products (total: ${allProductUrls.length})`);
    
    // If we have enough products, stop
    if (allProductUrls.length >= maxProducts) {
      this.log(`Reached target of ${maxProducts} products`);
      break;
    }
    
    // Try to navigate to next page
    const hasNextPage = await this.navigateToNextPage();
    if (!hasNextPage) {
      this.log('No more pages available');
      break;
    }
    
    currentPage++;
    
    // Wait for next page to load
    await this.waitForSearchResults();
  }
  
  this.log(`Pagination complete: ${allProductUrls.length} products from ${currentPage} pages`);
  return allProductUrls.slice(0, maxProducts);
}
```

### **2. Update ExtractProductLinksPhase**

**File:** `tts-chrome-extension/background/phases/extract-product-links-phase.js`

**Method to Update:**

```javascript
async extractProductLinksWithScrolling(tabId) {
  try {
    console.log(`[ExtractProductLinksPhase] Extracting product links with pagination`);

    const result = await chrome.scripting.executeScript({
      target: { tabId },
      func: (marketplace, maxProducts) => {
        return new Promise(async (resolve, reject) => {
          try {
            if (!window.SearchAutomation) {
              throw new Error('SearchAutomation not available');
            }

            // Use new pagination method instead of simple scrolling
            const productUrls = await window.SearchAutomation.extractProductLinksWithPagination(
              maxProducts, 
              5 // maxPages = 5
            );

            console.log(`[ExtractProductLinksPhase] Extracted ${productUrls.length} product URLs with pagination`);
            resolve(productUrls);

          } catch (error) {
            console.error(`[ExtractProductLinksPhase] Error in link extraction:`, error);
            reject(error);
          }
        });
      },
      args: [this.marketplace, this.maxProducts]
    });

    return result && result[0] && result[0].result ? result[0].result : [];

  } catch (error) {
    console.error(`[ExtractProductLinksPhase] Error extracting product links:`, error);
    return [];
  }
}
```

### **3. Improve Error Handling and Recovery**

**Update ExtractProductLinksPhase.execute():**

```javascript
async execute(context) {
  console.log(`[ExtractProductLinksPhase] Starting link extraction for ${this.marketplace}`);

  // Check if automation was stopped before starting
  await this.checkStopDuringOperation('link extraction initialization');

  // Create initial checkpoint
  this.createCheckpoint({
    action: 'starting_link_extraction',
    marketplace: this.marketplace,
    maxProducts: this.maxProducts
  });

  // Inject required extraction scripts
  await this.injectSearchAutomationScripts(context.tabId);

  // Extract product links with pagination
  const productUrls = await this.extractProductLinksWithScrolling(context.tabId);

  // Handle insufficient results gracefully
  if (productUrls.length === 0) {
    console.warn(`[ExtractProductLinksPhase] No product links found on search results page`);
    // Don't throw error, continue with empty array
  }

  // Validate and limit URLs
  this.extractedUrls = this.validateAndLimitUrls(productUrls);

  // Create completion checkpoint
  this.createCheckpoint({
    action: 'links_extraction_completed',
    urlCount: this.extractedUrls.length,
    urls: this.extractedUrls.slice(0, 5),
    attemptedPages: Math.min(5, Math.ceil(this.maxProducts / 20)) // Estimate pages attempted
  });

  console.log(`[ExtractProductLinksPhase] Completed with ${this.extractedUrls.length} URLs`);

  return {
    success: true,
    nextPhase: 'extract-product-data',
    data: {
      productUrls: this.extractedUrls,
      marketplace: this.marketplace,
      extractedCount: this.extractedUrls.length,
      maxProducts: this.maxProducts
    }
  };
}
```

## 🔄 **New Processing Flow**

### **ExtractProductLinksPhase Flow:**
1. **Inject scripts** → SearchAutomation available
2. **Page 1**: Scroll + extract links
3. **Check count**: If < maxProducts → navigate to next page
4. **Page 2-5**: Repeat until sufficient products or no more pages
5. **Return results**: Continue regardless of whether target is met
6. **Next phase**: ExtractProductDataPhase with available product list

### **Error Handling Strategy:**
- **No products found**: Continue with empty array
- **Pagination fails**: Continue with products already collected
- **Script injection fails**: Retry phase
- **Timeout**: Continue with partial results
- **Insufficient products after 5 pages**: Proceed anyway

## 🎯 **Benefits**

1. **Increased product yield**: Can collect from multiple pages
2. **Graceful degradation**: Doesn't fail if insufficient products
3. **Human-like behavior**: Pagination mimics real user behavior
4. **Robust error handling**: Always continues workflow
5. **Configurable limits**: Can adjust maxPages parameter
6. **Transparent flow**: Clear error propagation between phases

### **4. Update Error Recovery Logic**

**In AutomationController:**

```javascript
async recoverExtractProductLinksPhase(phase, structuredError) {
  console.log(`[AutomationController] Recovering ExtractProductLinksPhase`);

  // If no products found, continue to next phase anyway
  if (structuredError.message.includes('No product links found')) {
    console.warn(`[AutomationController] No products found, continuing with empty list`);
    return {
      success: true,
      nextPhase: 'extract-product-data',
      data: {
        productUrls: [],
        marketplace: phase.marketplace,
        extractedCount: 0,
        maxProducts: phase.maxProducts
      }
    };
  }

  // For other errors, retry the phase
  return await phase.retry(this.context);
}
```

## 📝 **Implementation Notes**

### **Marketplace-Specific Considerations:**
- **Etsy**: Uses `page=` parameter in URLs
- **eBay**: Uses `_pgn=` parameter for pagination
- **Amazon**: Uses `page=` parameter with different selectors

### **Human Behavior Simulation:**
- Random delays between page navigation (2-4 seconds)
- Scroll behavior on each page before extraction
- Click simulation with mouse movement
- Realistic waiting for page loads

### **Recovery Mechanisms:**
- Phase continues even with partial results
- No infinite loops or blocking errors
- Clear logging for debugging
- Checkpoint creation for resume capability

## 🚀 **Implementation Steps**

### **Step 1: Update SearchAutomation**
1. Add `navigateToNextPage()` method with marketplace-specific selectors
2. Add `extractProductLinksWithPagination()` method
3. Test pagination selectors on each marketplace

### **Step 2: Update ExtractProductLinksPhase**
1. Modify `extractProductLinksWithScrolling()` to use pagination
2. Update `execute()` method for graceful error handling
3. Add checkpoint tracking for pagination attempts

### **Step 3: Update AutomationController**
1. Add `recoverExtractProductLinksPhase()` method
2. Update error recovery logic to handle insufficient products
3. Ensure smooth transition to ExtractProductDataPhase

### **Step 4: Testing**
1. Test with different maxProducts values (10, 50, 200)
2. Test on marketplaces with limited search results
3. Verify graceful handling when no next page exists
4. Test error recovery scenarios
