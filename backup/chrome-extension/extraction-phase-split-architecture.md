# Chrome Extension Extraction Phase Split Architecture

## Executive Summary

This document outlines the architectural design for splitting the current `ExtractionPhase` into two separate, specialized phases: `ExtractProductLinksPhase` and `ExtractProductDataPhase`. This separation improves modularity, error handling, recovery mechanisms, and maintains compatibility with the existing Navigation Lifecycle Architecture.

## Current Architecture Analysis

### Existing ExtractionPhase Structure
The current `ExtractionPhase` combines two distinct operations:
1. **Product Link Extraction**: Extracting product URLs from search results pages with scrolling behavior
2. **Product Data Extraction**: Visiting individual product pages and extracting detailed product information

### Current Business Logic Flow
```
ExtractionPhase.execute() {
  1. Inject extraction scripts
  2. Extract product links from search results (Phase 1)
  3. For each product URL:
     a. Navigate to product page
     b. Extract product data
     c. Save to backend
     d. Update progress
     e. Add human-like delays
}
```

### Current Error Handling
- Granular retry mechanism for specific operations
- Operation tracking with `currentOperation` and `operationData`
- Checkpoint creation every 5 products
- Resume capability from `currentProductIndex`

## New Two-Phase Architecture

### Phase 1: ExtractProductLinksPhase

**Responsibility**: Extract product links from search results pages

**Key Features**:
- Handles search results page scrolling simulation
- Extracts product URLs using marketplace-specific selectors
- Validates extracted links
- Creates checkpoint with extracted URLs
- Implements human-like scrolling behavior

**Input**: Search results page (from SearchPhase)
**Output**: Array of validated product URLs

### Phase 2: ExtractProductDataPhase

**Responsibility**: Extract detailed data from individual product pages

**Key Features**:
- Receives product URLs from ExtractProductLinksPhase
- Navigates to each product page with human behavior simulation
- Extracts product data using marketplace-specific extractors
- Saves products to backend
- Tracks progress and creates periodic checkpoints
- Implements granular retry for individual product failures

**Input**: Array of product URLs
**Output**: Extracted product data and completion status

## Detailed Implementation Design

### 1. ExtractProductLinksPhase Implementation

```javascript
export class ExtractProductLinksPhase extends AutomationPhase {
  constructor(marketplace, maxProducts, config = {}) {
    super('extract-product-links', {
      marketplace,
      maxProducts,
      timeout: config.timeout || 60000,
      maxRetries: config.maxRetries || 2,
      ...config
    });
    
    this.marketplace = marketplace;
    this.maxProducts = maxProducts;
    this.humanTiming = new HumanTimingSimulator();
    this.extractedUrls = [];
  }

  async execute(context) {
    // 1. Inject required scripts
    await this.injectExtractionScripts(context.tabId);
    
    // 2. Extract product links with scrolling
    const productUrls = await this.extractProductLinksWithScrolling(context.tabId);
    
    // 3. Validate and limit URLs
    this.extractedUrls = this.validateAndLimitUrls(productUrls);
    
    // 4. Create checkpoint
    this.createCheckpoint({
      action: 'links_extracted',
      urlCount: this.extractedUrls.length,
      urls: this.extractedUrls
    });
    
    return {
      success: true,
      nextPhase: 'extract-product-data',
      data: {
        productUrls: this.extractedUrls,
        marketplace: this.marketplace,
        extractedCount: this.extractedUrls.length
      }
    };
  }
}
```

### 2. ExtractProductDataPhase Implementation

```javascript
export class ExtractProductDataPhase extends AutomationPhase {
  constructor(marketplace, productUrls, config = {}) {
    super('extract-product-data', {
      marketplace,
      timeout: config.timeout || 300000, // 5 minutes for multiple products
      maxRetries: config.maxRetries || 2,
      ...config
    });
    
    this.marketplace = marketplace;
    this.productUrls = productUrls || [];
    this.humanTiming = new HumanTimingSimulator();
    this.extractedProducts = [];
    this.currentProductIndex = 0;
    this.currentOperation = null;
    this.operationData = {};
  }

  async execute(context) {
    // Resume from checkpoint if available
    this.restoreFromCheckpoint();
    
    // Process each product URL
    for (let i = this.currentProductIndex; i < this.productUrls.length; i++) {
      await this.processProduct(i, context);
    }
    
    return {
      success: true,
      nextPhase: 'completed',
      data: {
        extractedCount: this.extractedProducts.length,
        attemptedCount: this.productUrls.length,
        marketplace: this.marketplace,
        products: this.extractedProducts
      }
    };
  }
}
```

## State Management and Data Flow

### Inter-Phase Data Transfer

**From ExtractProductLinksPhase to ExtractProductDataPhase**:
```javascript
// AutomationController.transitionToNextPhase()
case 'extract-product-data':
  this.stateMachine.transition('EXTRACTING_DATA', data);
  nextPhase = new ExtractProductDataPhase(
    this.context.schedule.marketplace,
    data.productUrls, // URLs from previous phase
    {
      maxRetries: 2,
      timeout: 300000
    }
  );
  break;
```

### Checkpoint Strategy

**ExtractProductLinksPhase Checkpoints**:
- Before scrolling simulation
- After URL extraction
- After URL validation

**ExtractProductDataPhase Checkpoints**:
- Every 5 products processed
- Before each product navigation
- After successful product save

### Recovery Mechanisms

**ExtractProductLinksPhase Recovery**:
- Retry entire link extraction process
- Re-inject scripts if needed
- Handle page reload during scrolling

**ExtractProductDataPhase Recovery**:
- Resume from last successful product index
- Retry individual product operations
- Skip failed products and continue

## Error Handling Architecture

### Phase-Specific Error Types

**ExtractProductLinksPhase Errors**:
- Script injection failures
- Search results not found
- Scrolling timeout
- No valid URLs extracted

**ExtractProductDataPhase Errors**:
- Product page navigation failures
- Data extraction failures
- Backend save failures
- Individual product timeouts

### Granular Retry Logic

```javascript
// ExtractProductDataPhase.retryCurrentOperation()
switch (this.currentOperation) {
  case 'navigating_to_product':
    return await this.navigateToProductWithHumanBehavior(
      context.tabId, 
      this.operationData.productUrl
    );
    
  case 'extracting_product_data':
    return await this.extractProductDataWithValidation(context.tabId);
    
  case 'saving_product':
    return await this.saveProductToBackend(this.operationData.productData);
}
```

## Navigation Lifecycle Integration

### State Machine Updates

**New States**:
- `EXTRACTING_LINKS`: During product link extraction
- `EXTRACTING_DATA`: During product data extraction

**State Transitions**:
```
SEARCHING → EXTRACTING_LINKS → EXTRACTING_DATA → COMPLETED
```

### AutomationController Integration

```javascript
// Updated transitionToNextPhase method
case 'extract-product-links':
  this.stateMachine.transition('EXTRACTING_LINKS', data);
  nextPhase = new ExtractProductLinksPhase(
    this.context.schedule.marketplace,
    this.context.schedule.maxProductsPerRun
  );
  break;

case 'extract-product-data':
  this.stateMachine.transition('EXTRACTING_DATA', data);
  nextPhase = new ExtractProductDataPhase(
    this.context.schedule.marketplace,
    data.productUrls
  );
  break;
```

## Content Script Compatibility

### Marketplace-Specific Scripts

**No changes required** to existing content scripts:
- `common-extractor.js`
- `etsy-extractor.js`
- `ebay-extractor.js`
- `amazon-extractor.js`
- `search-automation.js`

### Script Injection Strategy

**ExtractProductLinksPhase**: Injects search automation scripts
**ExtractProductDataPhase**: Injects product extraction scripts

Both phases use the same injection mechanism as the current ExtractionPhase.

## Human Behavior Simulation

### ExtractProductLinksPhase Timing
- Scrolling delays: 800-1500ms between scroll steps
- Page analysis time: 2000-5000ms
- Decision delays: 1000-3000ms

### ExtractProductDataPhase Timing
- Navigation delays: 1000-3000ms before navigation
- Page reading time: 2000-5000ms after page load
- Product transition delays: 3000-8000ms between products

## Backward Compatibility

### Legacy Support
- Existing automation schedules continue to work
- Current checkpoint format remains valid
- No changes to API endpoints required

### Migration Strategy
- Gradual rollout with feature flag
- Fallback to single ExtractionPhase if needed
- Comprehensive testing with existing schedules

## Implementation Timeline

### Phase 1: Core Implementation (Week 1)
- Create ExtractProductLinksPhase class
- Create ExtractProductDataPhase class
- Update AutomationController transitions

### Phase 2: Integration (Week 2)
- Update NavigationStateMachine
- Implement inter-phase data transfer
- Add comprehensive error handling

### Phase 3: Testing & Validation (Week 3)
- Unit tests for both phases
- Integration tests with real marketplaces
- Performance validation
- Backward compatibility verification

## Benefits of This Architecture

### 1. **Improved Modularity**
- Clear separation of concerns
- Easier testing and debugging
- Independent error handling

### 2. **Enhanced Error Recovery**
- Granular retry mechanisms
- Better failure isolation
- Improved resume capability

### 3. **Better Progress Tracking**
- Separate progress indicators for each phase
- More accurate time estimates
- Clearer user feedback

### 4. **Simplified Maintenance**
- Easier to modify individual phases
- Reduced complexity per phase
- Better code organization

### 5. **Scalability**
- Easier to add new extraction types
- Better resource management
- Improved parallel processing potential

## Risk Mitigation

### 1. **Data Loss Prevention**
- Comprehensive checkpoint system
- State persistence across phases
- Automatic recovery mechanisms

### 2. **Performance Optimization**
- Efficient memory usage
- Optimized script injection
- Reduced redundant operations

### 3. **Compatibility Assurance**
- Extensive testing with existing workflows
- Gradual migration strategy
- Fallback mechanisms

This architecture ensures that splitting the ExtractionPhase maintains all existing functionality while providing improved modularity, error handling, and maintainability for the Chrome extension product crawler system.

## Detailed Technical Specifications

### ExtractProductLinksPhase Technical Details

#### Class Structure
```javascript
export class ExtractProductLinksPhase extends AutomationPhase {
  constructor(marketplace, maxProducts, config = {}) {
    super('extract-product-links', {
      marketplace,
      maxProducts,
      timeout: config.timeout || 60000,
      maxRetries: config.maxRetries || 2,
      ...config
    });

    this.marketplace = marketplace;
    this.maxProducts = maxProducts;
    this.humanTiming = new HumanTimingSimulator();
    this.extractedUrls = [];
    this.scrollingComplete = false;

    // Generate unique instance ID for debugging
    this.instanceId = `extract_links_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  async execute(context) {
    console.log(`[ExtractProductLinksPhase] Starting link extraction for ${this.marketplace}`);

    // Check if automation was stopped before starting
    await this.checkStopDuringOperation('link extraction initialization');

    // Create initial checkpoint
    this.createCheckpoint({
      action: 'starting_link_extraction',
      marketplace: this.marketplace,
      maxProducts: this.maxProducts
    });

    // Inject required extraction scripts
    await this.injectSearchAutomationScripts(context.tabId);

    // Extract product links with scrolling behavior
    const productUrls = await this.extractProductLinksWithScrolling(context.tabId);

    if (productUrls.length === 0) {
      throw new Error('No product links found on search results page');
    }

    // Validate and limit URLs
    this.extractedUrls = this.validateAndLimitUrls(productUrls);

    // Create completion checkpoint
    this.createCheckpoint({
      action: 'links_extraction_completed',
      urlCount: this.extractedUrls.length,
      urls: this.extractedUrls.slice(0, 5) // Store first 5 URLs for debugging
    });

    return {
      success: true,
      nextPhase: 'extract-product-data',
      data: {
        productUrls: this.extractedUrls,
        marketplace: this.marketplace,
        extractedCount: this.extractedUrls.length,
        maxProducts: this.maxProducts
      }
    };
  }

  async injectSearchAutomationScripts(tabId) {
    try {
      console.log(`[ExtractProductLinksPhase] Injecting search automation scripts`);

      // Inject SearchAutomation script
      await chrome.scripting.executeScript({
        target: { tabId },
        files: ['content-scripts/search-automation.js']
      });

      // Wait for script initialization
      await this.delay(1000);

      console.log(`[ExtractProductLinksPhase] Search automation scripts injected successfully`);

    } catch (error) {
      console.error(`[ExtractProductLinksPhase] Failed to inject scripts:`, error);
      throw new Error(`Script injection failed: ${error.message}`);
    }
  }

  async extractProductLinksWithScrolling(tabId) {
    try {
      console.log(`[ExtractProductLinksPhase] Extracting product links with scrolling`);

      const result = await chrome.scripting.executeScript({
        target: { tabId },
        func: (marketplace, maxProducts) => {
          return new Promise(async (resolve, reject) => {
            try {
              if (!window.SearchAutomation) {
                throw new Error('SearchAutomation not available');
              }

              // Simulate scrolling to load more products
              await window.SearchAutomation.simulateScrolling();

              // Extract product links
              const productUrls = window.SearchAutomation.extractProductLinks(maxProducts);

              console.log(`[ExtractProductLinksPhase] Extracted ${productUrls.length} product URLs`);
              resolve(productUrls);

            } catch (error) {
              console.error(`[ExtractProductLinksPhase] Error in link extraction:`, error);
              reject(error);
            }
          });
        },
        args: [this.marketplace, this.maxProducts]
      });

      return result && result[0] && result[0].result ? result[0].result : [];

    } catch (error) {
      console.error(`[ExtractProductLinksPhase] Error extracting product links:`, error);
      return [];
    }
  }

  validateAndLimitUrls(urls) {
    const validUrls = urls.filter(url => {
      try {
        new URL(url);
        return this.isValidProductUrl(url);
      } catch {
        return false;
      }
    });

    return validUrls.slice(0, this.maxProducts);
  }

  isValidProductUrl(url) {
    const patterns = {
      etsy: /\/listing\/\d+/,
      ebay: /\/itm\/\d+/,
      amazon: /\/(dp|gp\/product)\/[A-Z0-9]+/
    };

    return patterns[this.marketplace]?.test(url) || false;
  }

  // Override canResume for link extraction specific logic
  async canResume(navigationEvent) {
    console.log(`[ExtractProductLinksPhase] Checking if can resume after navigation`);

    if (navigationEvent.url) {
      try {
        const eventDomain = new URL(navigationEvent.url).hostname;
        const expectedDomain = this.getMarketplaceDomain(this.marketplace);

        if (eventDomain.includes(expectedDomain)) {
          // Check if still on search results page
          const isSearchPage = this.isSearchResultsPage(navigationEvent.url);
          if (isSearchPage) {
            console.log(`[ExtractProductLinksPhase] Can resume - still on search results`);
            return true;
          }
        }
      } catch (error) {
        console.error(`[ExtractProductLinksPhase] Error checking domains:`, error);
      }
    }

    return this.retryCount < this.maxRetries;
  }

  isSearchResultsPage(url) {
    const searchPatterns = {
      etsy: ['/search', '?q='],
      ebay: ['/sch/', '?_nkw='],
      amazon: ['/s?', '/s/', '?k=']
    };

    const patterns = searchPatterns[this.marketplace] || [];
    return patterns.some(pattern => url.toLowerCase().includes(pattern));
  }

  getMarketplaceDomain(marketplace) {
    const domains = {
      'etsy': 'etsy.com',
      'ebay': 'ebay.com',
      'amazon': 'amazon.com'
    };
    return domains[marketplace] || '';
  }

  async delay(ms) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        if (this.checkStopRequested()) {
          reject(new Error('Automation stopped by user during delay'));
        } else {
          resolve();
        }
      }, ms);

      if (this.checkStopRequested()) {
        clearTimeout(timeoutId);
        reject(new Error('Automation stopped by user before delay'));
      }
    });
  }
}
```

### ExtractProductDataPhase Technical Details

#### Class Structure
```javascript
export class ExtractProductDataPhase extends AutomationPhase {
  constructor(marketplace, productUrls, config = {}) {
    super('extract-product-data', {
      marketplace,
      timeout: config.timeout || 300000, // 5 minutes for multiple products
      maxRetries: config.maxRetries || 2,
      ...config
    });

    this.marketplace = marketplace;
    this.productUrls = productUrls || [];
    this.humanTiming = new HumanTimingSimulator();
    this.extractedProducts = [];

    // Operation tracking for granular retry
    this.currentOperation = null;
    this.currentProductIndex = 0;
    this.operationData = {};

    // Generate unique instance ID for debugging
    this.instanceId = `extract_data_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    console.log(`[ExtractProductDataPhase] Initialized for ${marketplace}, ${productUrls.length} products`);
  }

  async execute(context) {
    console.log(`[ExtractProductDataPhase] Starting data extraction for ${this.productUrls.length} products`);

    // Check if automation was stopped before starting
    await this.checkStopDuringOperation('data extraction initialization');

    // Create initial checkpoint
    this.createCheckpoint({
      action: 'starting_data_extraction',
      marketplace: this.marketplace,
      totalProducts: this.productUrls.length
    });

    // Inject required extraction scripts
    await this.injectProductExtractionScripts(context.tabId);

    // Restore from checkpoint if available
    this.restoreFromCheckpoint();

    // Update progress tracking
    this.updateProgress(this.currentProductIndex, this.productUrls.length, 'Starting product data extraction');

    // Process each product URL
    for (let i = this.currentProductIndex; i < this.productUrls.length; i++) {
      await this.processProduct(i, context);
    }

    // Create completion checkpoint
    this.createCheckpoint({
      action: 'data_extraction_completed',
      totalExtracted: this.extractedProducts.length,
      totalAttempted: this.productUrls.length
    });

    return {
      success: true,
      nextPhase: 'completed',
      data: {
        extractedCount: this.extractedProducts.length,
        attemptedCount: this.productUrls.length,
        marketplace: this.marketplace,
        products: this.extractedProducts
      }
    };
  }

  async processProduct(index, context) {
    const productUrl = this.productUrls[index];
    this.currentProductIndex = index;

    try {
      // Check if automation was stopped before each product
      await this.checkStopDuringOperation(`product extraction ${index + 1}/${this.productUrls.length}`);

      console.log(`[ExtractProductDataPhase] Processing product ${index + 1}/${this.productUrls.length}: ${productUrl}`);

      // Navigate to product page
      this.currentOperation = 'navigating_to_product';
      this.operationData = { productUrl: productUrl, index: index };

      await this.navigateToProductWithHumanBehavior(context.tabId, productUrl);

      // Extract product data
      this.currentOperation = 'extracting_product_data';
      const productData = await this.extractProductDataWithValidation(context.tabId);

      if (productData && productData.success) {
        // Save to backend
        this.currentOperation = 'saving_product';
        this.operationData.productData = productData.data;

        const saveResult = await this.saveProductToBackend(productData.data);

        if (saveResult.success) {
          this.extractedProducts.push(productData.data);
          console.log(`[ExtractProductDataPhase] Successfully extracted: ${productData.data.title}`);
        } else {
          console.error(`[ExtractProductDataPhase] Failed to save product: ${saveResult.error}`);
        }
      } else {
        console.warn(`[ExtractProductDataPhase] Failed to extract product data from: ${productUrl}`);
      }

      // Clear operation after successful completion
      this.currentOperation = null;
      this.operationData = {};

      // Update progress
      this.updateProgress(index + 1, this.productUrls.length, `Extracted ${this.extractedProducts.length} products`);

      // Create checkpoint every 5 products
      if ((index + 1) % 5 === 0) {
        this.createCheckpoint({
          action: 'progress_checkpoint',
          extractedCount: this.extractedProducts.length,
          currentIndex: index + 1,
          totalProducts: this.productUrls.length
        });
      }

      // Add realistic delay between products
      await this.humanTiming.getProductTransitionDelay();

    } catch (error) {
      // Check if this is a stop error
      if (error.message && error.message.includes('stopped by user')) {
        throw error; // Re-throw stop errors to stop the loop
      }

      console.error(`[ExtractProductDataPhase] Error processing product ${index + 1}:`, error);
      console.error(`[ExtractProductDataPhase] Error occurred during operation: ${this.currentOperation}`);

      // Store operation data for potential retry
      if (this.currentOperation === 'extracting_product_data' && productData) {
        this.operationData.productData = productData;
      }

      throw error; // Let automation-controller handle retry with granular logic
    }
  }

  async injectProductExtractionScripts(tabId) {
    try {
      console.log(`[ExtractProductDataPhase] Injecting product extraction scripts`);

      // Inject CommonExtractor script
      await chrome.scripting.executeScript({
        target: { tabId },
        files: ['content-scripts/common-extractor.js']
      });

      // Inject marketplace-specific extractor
      const extractorFiles = {
        etsy: 'content-scripts/etsy-extractor.js',
        ebay: 'content-scripts/ebay-extractor.js',
        amazon: 'content-scripts/amazon-extractor.js'
      };

      if (extractorFiles[this.marketplace]) {
        await chrome.scripting.executeScript({
          target: { tabId },
          files: [extractorFiles[this.marketplace]]
        });
      }

      // Wait for scripts to initialize
      await this.delay(1000);

      console.log(`[ExtractProductDataPhase] Product extraction scripts injected successfully`);

    } catch (error) {
      console.error(`[ExtractProductDataPhase] Failed to inject extraction scripts:`, error);
      throw new Error(`Script injection failed: ${error.message}`);
    }
  }

  restoreFromCheckpoint() {
    const latestCheckpoint = this.getLatestCheckpoint();
    if (latestCheckpoint && latestCheckpoint.data.currentIndex) {
      this.currentProductIndex = latestCheckpoint.data.currentIndex;
      console.log(`[ExtractProductDataPhase] Restored from checkpoint at product ${this.currentProductIndex}`);
    }
  }

  // Granular retry method for specific operations
  async retryCurrentOperation(context) {
    console.log(`[ExtractProductDataPhase] Retrying operation: ${this.currentOperation}`);

    switch (this.currentOperation) {
      case 'navigating_to_product':
        const { productUrl } = this.operationData;
        return await this.navigateToProductWithHumanBehavior(context.tabId, productUrl);

      case 'extracting_product_data':
        return await this.extractProductDataWithValidation(context.tabId);

      case 'saving_product':
        const { productData } = this.operationData;
        return await this.saveProductToBackend(productData);

      default:
        throw new Error(`Cannot retry unknown operation: ${this.currentOperation}`);
    }
  }

  // Override retry method for granular retry logic
  async retry(context = {}) {
    if (this.retryCount >= this.maxRetries) {
      throw new Error(`Phase ${this.name} exceeded maximum retries (${this.maxRetries})`);
    }

    this.retryCount++;
    console.log(`[ExtractProductDataPhase] Retry attempt ${this.retryCount}/${this.maxRetries}`);

    // Granular retry: only retry current operation
    if (this.currentOperation) {
      console.log(`[ExtractProductDataPhase] Granular retry for operation: ${this.currentOperation}`);
      try {
        const result = await this.retryCurrentOperation(context);

        // If retry succeeds, continue execution from where we left off
        if (this.currentOperation === 'navigating_to_product') {
          // Navigation succeeded, continue with data extraction
          this.currentOperation = 'extracting_product_data';
          const productData = await this.extractProductDataWithValidation(context.tabId);

          if (productData && productData.success) {
            this.currentOperation = 'saving_product';
            this.operationData.productData = productData.data;
            await this.saveProductToBackend(productData.data);
            this.extractedProducts.push(productData.data);
          }

          // Move to next product
          this.currentProductIndex++;
          this.currentOperation = null;
          this.operationData = {};

          return await this.execute(context);
        } else {
          // For other operations, return the result
          return result;
        }
      } catch (error) {
        console.error(`[ExtractProductDataPhase] Granular retry failed:`, error);
        throw error;
      }
    } else {
      // Fallback: restart entire phase if no specific operation
      console.log(`[ExtractProductDataPhase] Full restart retry`);
      return await this.start({ ...this.context, ...context, isRetry: true });
    }
  }

  // Navigation and extraction methods (same as current ExtractionPhase)
  async navigateToProductWithHumanBehavior(tabId, productUrl) {
    console.log(`[ExtractProductDataPhase] Navigating to product: ${productUrl}`);

    // Add human decision delay before navigation
    await this.humanTiming.getDecisionDelay();

    // Navigate to product page
    await chrome.tabs.update(tabId, { url: productUrl });

    // Wait for page to load
    await this.waitForPageLoad(tabId);

    // Add human reading time after page loads
    await this.humanTiming.getPageReadingTime();
  }

  async waitForPageLoad(tabId) {
    const maxWaitTime = 30000; // 30 seconds max
    const checkInterval = 1000; // Check every second
    let waitTime = 0;

    while (waitTime < maxWaitTime) {
      try {
        // Check if automation was stopped during waiting
        await this.checkStopDuringOperation('page load waiting');

        const tab = await chrome.tabs.get(tabId);

        if (tab.status === 'complete') {
          console.log(`[ExtractProductDataPhase] Page loaded after ${waitTime}ms`);
          return;
        }

        await this.delay(checkInterval);
        waitTime += checkInterval;

      } catch (error) {
        // Check if this is a stop error
        if (error.message && error.message.includes('stopped by user')) {
          throw error; // Re-throw stop errors
        }

        console.error(`[ExtractProductDataPhase] Error checking page load:`, error);
        throw new Error(`Failed to check page load: ${error.message}`);
      }
    }

    throw new Error(`Page load timeout after ${maxWaitTime}ms`);
  }

  async extractProductDataWithValidation(tabId) {
    try {
      console.log(`[ExtractProductDataPhase] Extracting product data with validation`);

      const result = await chrome.scripting.executeScript({
        target: { tabId },
        func: function(marketplace) {
          // This function runs in the context of the web page
          console.log('[ExtractProductDataPhase] Executing extraction for marketplace:', marketplace);

          let extractor;
          switch (marketplace) {
            case 'etsy':
              extractor = window.EtsyExtractor;
              break;
            case 'ebay':
              extractor = window.EbayExtractor;
              break;
            case 'amazon':
              extractor = window.AmazonExtractor;
              break;
            default:
              console.error('[ExtractProductDataPhase] Unknown marketplace:', marketplace);
              return { error: 'Unknown marketplace' };
          }

          if (!extractor) {
            console.error('[ExtractProductDataPhase] Extractor not found for', marketplace);
            return { error: `${marketplace} extractor not available` };
          }

          if (!extractor.extractProductData) {
            console.error('[ExtractProductDataPhase] extractProductData method not found');
            return { error: 'extractProductData method not found' };
          }

          try {
            console.log('[ExtractProductDataPhase] Calling extractProductData...');
            const result = extractor.extractProductData();
            console.log('[ExtractProductDataPhase] Extraction result:', result);

            if (!result) {
              return { error: 'No product data extracted' };
            }

            return result;
          } catch (error) {
            console.error('[ExtractProductDataPhase] Error during extraction:', error);
            return { error: error.message };
          }
        },
        args: [this.marketplace]
      });

      console.log('[ExtractProductDataPhase] executeScript results:', result);
      const extractionResult = result[0].result;

      if (!extractionResult) {
        return { success: false, error: 'No result returned from extraction' };
      }

      if (extractionResult.error) {
        return { success: false, error: extractionResult.error };
      }

      console.log(`[ExtractProductDataPhase] Successfully extracted product: ${extractionResult.title}`);
      return {
        success: true,
        data: extractionResult
      };

    } catch (error) {
      console.error(`[ExtractProductDataPhase] Error extracting product data:`, error);
      return { success: false, error: error.message };
    }
  }

  async saveProductToBackend(productData) {
    try {
      // Use the API client from the context
      const apiClient = this.context.apiClient;

      if (!apiClient) {
        throw new Error('API client not available');
      }

      const result = await apiClient.createCrawledProduct(productData);
      return result;

    } catch (error) {
      console.error(`[ExtractProductDataPhase] Error saving product to backend:`, error);
      return { success: false, error: error.message };
    }
  }

  // Override canResume for data extraction specific logic
  async canResume(navigationEvent) {
    console.log(`[ExtractProductDataPhase] Checking if can resume after navigation event`);

    // Data extraction phase can resume if we're still on the same marketplace
    if (navigationEvent.url) {
      try {
        const eventDomain = new URL(navigationEvent.url).hostname;
        const expectedDomain = this.getMarketplaceDomain(this.marketplace);

        if (eventDomain.includes(expectedDomain)) {
          console.log(`[ExtractProductDataPhase] Can resume - still on ${this.marketplace}`);
          return true;
        }
      } catch (error) {
        console.error(`[ExtractProductDataPhase] Error checking domains:`, error);
      }
    }

    return this.retryCount < this.maxRetries;
  }

  getMarketplaceDomain(marketplace) {
    const domains = {
      'etsy': 'etsy.com',
      'ebay': 'ebay.com',
      'amazon': 'amazon.com'
    };
    return domains[marketplace] || '';
  }

  async delay(ms) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        if (this.checkStopRequested()) {
          reject(new Error('Automation stopped by user during delay'));
        } else {
          resolve();
        }
      }, ms);

      if (this.checkStopRequested()) {
        clearTimeout(timeoutId);
        reject(new Error('Automation stopped by user before delay'));
      }
    });
  }
}
```

### AutomationController Integration

#### Updated transitionToNextPhase Method
```javascript
async transitionToNextPhase(nextPhaseName, data) {
  console.log(`[AutomationController] Transitioning to next phase: ${nextPhaseName}`);

  let nextPhase;

  switch (nextPhaseName) {
    case 'search':
      this.stateMachine.transition('SEARCHING', data);
      nextPhase = new SearchPhase(
        this.context.schedule.keywords.split(',')[0].trim(),
        this.context.schedule.marketplace
      );
      break;

    case 'extract-product-links':
      this.stateMachine.transition('EXTRACTING_LINKS', data);
      nextPhase = new ExtractProductLinksPhase(
        this.context.schedule.marketplace,
        this.context.schedule.maxProductsPerRun
      );
      break;

    case 'extract-product-data':
      this.stateMachine.transition('EXTRACTING_DATA', data);
      nextPhase = new ExtractProductDataPhase(
        this.context.schedule.marketplace,
        data.productUrls
      );
      break;

    case 'completed':
      this.stateMachine.transition('COMPLETED', data);
      await this.completeAutomation(data);
      return;

    default:
      throw new Error(`Unknown next phase: ${nextPhaseName}`);
  }

  if (nextPhase) {
    await this.executePhase(nextPhase);
  }
}
```

#### Updated NavigationStateMachine States
```javascript
defineTransitions() {
  return {
    'IDLE': ['NAVIGATING', 'ERROR'],
    'NAVIGATING': ['SEARCHING', 'ERROR', 'IDLE', 'RECOVERING', 'STOPPED'],
    'SEARCHING': ['EXTRACTING_LINKS', 'NAVIGATING', 'ERROR', 'IDLE', 'RECOVERING', 'STOPPED'],
    'EXTRACTING_LINKS': ['EXTRACTING_DATA', 'SEARCHING', 'NAVIGATING', 'ERROR', 'IDLE', 'RECOVERING', 'STOPPED'],
    'EXTRACTING_DATA': ['COMPLETED', 'EXTRACTING_LINKS', 'SEARCHING', 'NAVIGATING', 'ERROR', 'IDLE', 'RECOVERING', 'STOPPED'],
    'COMPLETED': ['IDLE', 'NAVIGATING'],
    'ERROR': ['IDLE', 'NAVIGATING', 'RECOVERING', 'STOPPED'],
    'RECOVERING': ['IDLE', 'NAVIGATING', 'SEARCHING', 'EXTRACTING_LINKS', 'EXTRACTING_DATA', 'ERROR', 'STOPPED'],
    'STOPPED': ['IDLE']
  };
}
```

This comprehensive technical specification ensures that the split architecture maintains all existing functionality while providing the improved modularity and error handling capabilities outlined in the main architecture document.
