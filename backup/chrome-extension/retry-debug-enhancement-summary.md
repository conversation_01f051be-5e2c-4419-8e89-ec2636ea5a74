# Retry Debug Enhancement Summary

## 🐛 **Vấn đề nghi ngờ:**
Khi có lỗi timeout, các phase đã trả về error mặc dù tại các phase đó sẽ retry. Nhưng vì trả về lỗi mà NavigationMonitor sẽ ghi nhận để retry lại lần nữa, dẫn đến việc retry nhiều lớp gây lỗi.

## 🔧 **<PERSON><PERSON><PERSON> thay đổi đã thực hiện:**

### 1. **Enhanced NavigationMonitor Debug (navigation-monitor.js)**

#### **Thêm stack trace vào handleNavigationError:**
```javascript
async handleNavigationError(details) {
  const event = {
    type: 'error',
    url: details.url,
    error: details.error,
    frameId: details.frameId,
    timestamp: Date.now(),
    details,
    stackTrace: new Error().stack // Add stack trace for debugging
  };

  this.recordEvent(event);
  console.error(`[NavigationMonitor] Navigation error: ${details.error} for ${details.url}`);
  console.error(`[NavigationMonitor] Stack trace:`, event.stackTrace);
  
  // Debug: Check current controller state
  if (this.controller) {
    console.log(`[NavigationMonitor] Controller state:`, {
      currentPhase: this.controller.currentPhase?.name,
      isRecovering: this.controller.isRecovering,
      isStopped: this.controller.isStopped,
      phaseRetryCount: this.controller.currentPhase?.retryCount,
      phaseMaxRetries: this.controller.currentPhase?.maxRetries
    });
  }
}
```

#### **Thêm stack trace vào handleNavigationTimeout:**
```javascript
setNavigationTimeout(url) {
  this.navigationTimeout = setTimeout(async () => {
    const timeoutEvent = {
      type: 'timeout',
      url,
      timestamp: Date.now(),
      stackTrace: new Error().stack // Add stack trace for debugging
    };

    // Debug: Check current controller state
    if (this.controller) {
      console.log(`[NavigationMonitor] Controller state at timeout:`, {
        currentPhase: this.controller.currentPhase?.name,
        isRecovering: this.controller.isRecovering,
        isStopped: this.controller.isStopped,
        phaseRetryCount: this.controller.currentPhase?.retryCount,
        phaseMaxRetries: this.controller.currentPhase?.maxRetries
      });
    }
  }, 30000);
}
```

### 2. **Enhanced AutomationController Debug (automation-controller.js)**

#### **Thêm debug info vào handlePhaseError:**
```javascript
async handlePhaseError(phase, error) {
  console.error(`[AutomationController] Handling error in phase ${phase.name}:`, error);
  console.error(`[AutomationController] Phase error stack trace:`, error.stack);
  
  // Debug: Log current state and retry information
  console.log(`[AutomationController] Phase error debug info:`, {
    phaseName: phase.name,
    phaseRetryCount: phase.retryCount,
    phaseMaxRetries: phase.maxRetries,
    isRecovering: this.isRecovering,
    isStopped: this.isStopped,
    errorMessage: error.message,
    errorType: error.constructor.name,
    callStack: new Error().stack
  });
}
```

#### **Thêm debug info vào attemptRecovery:**
```javascript
async attemptRecovery(phase, error) {
  console.log(`[AutomationController] Recovery debug info:`, {
    phaseName: phase.name,
    phaseRetryCount: phase.retryCount,
    phaseMaxRetries: phase.maxRetries,
    isRecovering: this.isRecovering,
    errorMessage: error.message,
    recoveryCallStack: new Error().stack
  });

  console.log(`[AutomationController] Calling phase.retry() for ${phase.name} (attempt ${phase.retryCount + 1}/${phase.maxRetries})`);
  
  const result = await phase.retry(this.context);
  
  console.log(`[AutomationController] Phase retry successful for ${phase.name}:`, result);
}
```

#### **Thêm debug info vào handleNavigationTimeout:**
```javascript
async handleNavigationTimeout(event) {
  console.warn(`[AutomationController] Timeout stack trace:`, event.stackTrace);
  
  console.log(`[AutomationController] Navigation timeout debug info:`, {
    currentPhase: this.currentPhase?.name,
    phaseRetryCount: this.currentPhase?.retryCount,
    phaseMaxRetries: this.currentPhase?.maxRetries,
    isRecovering: this.isRecovering,
    isStopped: this.isStopped,
    timeoutUrl: event.url,
    timeoutCallStack: new Error().stack
  });

  if (this.currentPhase) {
    console.log(`[AutomationController] Calling currentPhase.retry() due to navigation timeout for ${this.currentPhase.name}`);
    await this.currentPhase.retry(this.context);
  }
}
```

### 3. **Enhanced AutomationPhase Debug (automation-phase.js)**

#### **Thêm debug info vào retry method:**
```javascript
async retry(context = {}) {
  this.retryCount++;
  console.log(`[AutomationPhase:${this.name}] Retry attempt ${this.retryCount}/${this.maxRetries}`);
  console.log(`[AutomationPhase:${this.name}] Retry debug info:`, {
    phaseName: this.name,
    retryCount: this.retryCount,
    maxRetries: this.maxRetries,
    currentStatus: this.status,
    contextKeys: Object.keys(context),
    isRetry: true,
    retryCallStack: new Error().stack
  });
  
  console.log(`[AutomationPhase:${this.name}] Calling start() for retry with context:`, { ...this.context, ...context, isRetry: true });
  return await this.start({ ...this.context, ...context, isRetry: true });
}
```

#### **Thêm debug info vào start method:**
```javascript
async start(context = {}) {
  console.log(`[AutomationPhase:${this.name}] Start debug info:`, {
    phaseName: this.name,
    retryCount: this.retryCount,
    maxRetries: this.maxRetries,
    isRetry: context.isRetry || false,
    contextKeys: Object.keys(context),
    startCallStack: new Error().stack
  });
}
```

### 4. **Test Interface (test-retry-debug.html)**

Tạo test interface để mô phỏng các tình huống:
- **Test 1:** Navigation Timeout → Phase Retry
- **Test 2:** Navigation Error → Recovery → Phase Retry  
- **Test 3:** Monitor Retry Call Stack

### 5. **Test Handlers (service-worker.js)**

Thêm test handlers để mô phỏng:
- `handleTestNavigationTimeout()` - Mô phỏng navigation timeout
- `handleTestNavigationError()` - Mô phỏng navigation error với ERR_ABORTED
- `handleTestRetryStack()` - Mô phỏng retry call stack

## 🔍 **Cách sử dụng để debug:**

1. **Mở test interface:**
   ```
   chrome-extension://[extension-id]/test-retry-debug.html
   ```

2. **Mở Chrome DevTools Console**

3. **Chạy các test và quan sát:**
   - Stack traces của mỗi retry call
   - Controller state tại mỗi thời điểm
   - Phase retry count và max retries
   - Nested retry patterns

4. **Tìm kiếm patterns:**
   ```
   NavigationMonitor.handleNavigationTimeout()
   → AutomationController.handleNavigationTimeout()
   → Phase.retry()
   → Phase.start()
   → [Phase fails again]
   → AutomationController.handlePhaseError()
   → AutomationController.attemptRecovery()
   → Phase.retry() [DUPLICATE RETRY!]
   ```

## 🎯 **Mục tiêu:**

Với các debug enhancements này, bạn sẽ có thể:
- **Xác định chính xác** khi nào có multiple retry calls
- **Theo dõi call stack** để tìm nguồn gốc của nested retries
- **Phát hiện race conditions** giữa NavigationMonitor và AutomationController
- **Xác minh nghi ngờ** về retry nhiều lớp gây lỗi

## 📋 **Files Modified:**

1. **navigation-monitor.js** - Enhanced error/timeout debugging
2. **automation-controller.js** - Enhanced recovery debugging  
3. **automation-phase.js** - Enhanced retry debugging
4. **service-worker.js** - Added test handlers
5. **test-retry-debug.html** - New test interface
