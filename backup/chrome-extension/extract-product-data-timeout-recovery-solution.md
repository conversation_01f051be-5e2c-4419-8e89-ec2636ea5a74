# Extract Product Data Phase Timeout Recovery Solution

## Problem Analysis

### Current Issues

1. **Timeout Propagation**: When `extract-product-data` phase times out (300s), the error propagates incorrectly through the call stack, causing confusion about which phase actually failed.

2. **Recovery Logic Confusion**: The recovery mechanism attempts to recover the wrong phase due to error propagation, leading to:
   - "search phase failed" when actually "extract-product-data" timed out
   - Recovery attempts on wrong phase instances
   - Duplicate recovery attempts

3. **Product-Level vs Phase-Level Failures**: Current implementation doesn't distinguish between:
   - Individual product extraction failures (should skip to next product)
   - Phase-level timeouts (should retry entire phase)

4. **Error Context Loss**: When errors bubble up through multiple phases, the original context and phase information gets lost.

## Root Cause Analysis

### Error Propagation Chain
```
extract-product-data timeout → handlePhaseError → attemptRecovery → 
recovery fails → error re-thrown → executePhase catches → 
reports as "search phase failed"
```

### Current Recovery Flow Issues
```javascript
// automation-controller.js - Current problematic flow
async executePhase(phase) {
  try {
    const result = await phase.start(this.context);
    // ...
  } catch (error) {
    console.error(`Phase ${phase.name} failed:`, error); // Wrong phase reported
    await this.handlePhaseError(phase, error); // Attempts recovery on wrong phase
    throw error; // Re-throws, causing further confusion
  }
}
```

## Solution Architecture

### 1. Enhanced Error Classification

#### Error Types
```javascript
class PhaseError extends Error {
  constructor(message, phaseName, errorType, originalError = null) {
    super(message);
    this.name = 'PhaseError';
    this.phaseName = phaseName;
    this.errorType = errorType; // 'timeout', 'product_failure', 'navigation_failure', etc.
    this.originalError = originalError;
    this.timestamp = Date.now();
  }
}

class ProductExtractionError extends PhaseError {
  constructor(message, productUrl, productIndex, operation, originalError = null) {
    super(message, 'extract-product-data', 'product_failure', originalError);
    this.productUrl = productUrl;
    this.productIndex = productIndex;
    this.operation = operation; // 'navigating', 'extracting', 'saving'
  }
}
```

### 2. Product-Level Skip Strategy

#### Enhanced ExtractProductDataPhase
```javascript
export class ExtractProductDataPhase extends AutomationPhase {
  constructor(marketplace, productUrls, config = {}) {
    super('extract-product-data', {
      marketplace,
      timeout: config.timeout || 300000,
      maxRetries: config.maxRetries || 2,
      productTimeout: config.productTimeout || 30000, // Per-product timeout
      maxProductRetries: config.maxProductRetries || 2,
      skipFailedProducts: config.skipFailedProducts !== false, // Default: true
      ...config
    });
    
    this.marketplace = marketplace;
    this.productUrls = productUrls || [];
    this.extractedProducts = [];
    this.failedProducts = [];
    this.skippedProducts = [];
    
    // Enhanced operation tracking
    this.currentProductIndex = 0;
    this.currentOperation = null;
    this.operationData = {};
    this.productRetryCount = 0;
  }

  async processProduct(index, context) {
    const productUrl = this.productUrls[index];
    this.currentProductIndex = index;
    this.productRetryCount = 0;

    while (this.productRetryCount <= this.config.maxProductRetries) {
      try {
        await this.processProductWithTimeout(index, context);
        return; // Success, move to next product
      } catch (error) {
        this.productRetryCount++;
        
        if (this.shouldSkipProduct(error, this.productRetryCount)) {
          console.warn(`[ExtractProductDataPhase] Skipping product ${index + 1} after ${this.productRetryCount} attempts: ${error.message}`);
          this.skippedProducts.push({
            url: productUrl,
            index: index,
            error: error.message,
            attempts: this.productRetryCount
          });
          return; // Skip to next product
        }
        
        if (this.productRetryCount > this.config.maxProductRetries) {
          throw new ProductExtractionError(
            `Failed to extract product after ${this.productRetryCount} attempts`,
            productUrl,
            index,
            this.currentOperation,
            error
          );
        }
        
        // Wait before retry
        await this.delay(2000 * this.productRetryCount);
      }
    }
  }

  shouldSkipProduct(error, retryCount) {
    if (!this.config.skipFailedProducts) return false;
    
    // Skip on timeout or network errors after max retries
    const skipConditions = [
      error.message.includes('timeout'),
      error.message.includes('Navigation failed'),
      error.message.includes('Page load timeout'),
      error.message.includes('Network error'),
      retryCount > this.config.maxProductRetries
    ];
    
    return skipConditions.some(condition => condition);
  }
}
```

### 3. Phase-Specific Recovery Logic

#### Enhanced AutomationController Recovery
```javascript
class AutomationController {
  async handlePhaseError(phase, error) {
    console.error(`[AutomationController] Handling error in phase ${phase.name}:`, error);
    
    // Create structured error with proper context
    const structuredError = this.createStructuredError(phase, error);
    
    // Log with proper phase identification
    console.error(`[AutomationController] Structured error:`, {
      actualFailedPhase: structuredError.phaseName,
      errorType: structuredError.errorType,
      originalMessage: structuredError.message,
      currentPhase: this.currentPhase?.name,
      phaseInstance: phase.instanceId
    });

    this.stateMachine.transition('ERROR', {
      phase: structuredError.phaseName, // Use actual failed phase
      error: structuredError.message,
      errorType: structuredError.errorType
    });

    // Phase-specific recovery strategy
    if (await this.canRecoverFromError(phase, structuredError)) {
      await this.attemptPhaseSpecificRecovery(phase, structuredError);
    } else {
      await this.abortAutomation(`Phase ${structuredError.phaseName} failed: ${structuredError.message}`);
    }
  }

  createStructuredError(phase, error) {
    // If it's already a structured error, return as-is
    if (error instanceof PhaseError) {
      return error;
    }
    
    // Determine actual failed phase from error context
    let actualPhaseName = phase.name;
    let errorType = 'unknown';
    
    if (error.message.includes('timed out after')) {
      errorType = 'timeout';
      // Extract phase name from timeout message
      const match = error.message.match(/Phase (\w+(?:-\w+)*) timed out/);
      if (match) {
        actualPhaseName = match[1];
      }
    }
    
    return new PhaseError(error.message, actualPhaseName, errorType, error);
  }

  async attemptPhaseSpecificRecovery(phase, structuredError) {
    try {
      console.log(`[AutomationController] Attempting recovery for phase: ${structuredError.phaseName}`);
      
      // Prevent duplicate recovery
      if (this.isRecovering) {
        console.log(`[AutomationController] Recovery already in progress, skipping`);
        return;
      }
      
      this.isRecovering = true;
      this.stateMachine.transition('RECOVERING', {
        phase: structuredError.phaseName,
        error: structuredError.message
      });

      let result;
      
      // Phase-specific recovery strategies
      switch (structuredError.phaseName) {
        case 'extract-product-data':
          result = await this.recoverExtractProductDataPhase(phase, structuredError);
          break;
        case 'extract-product-links':
          result = await this.recoverExtractProductLinksPhase(phase, structuredError);
          break;
        case 'search':
          result = await this.recoverSearchPhase(phase, structuredError);
          break;
        default:
          result = await phase.retry(this.context);
      }

      await this.handlePhaseCompletion(phase, result);
      this.isRecovering = false;

    } catch (recoveryError) {
      console.error(`[AutomationController] Recovery failed for ${structuredError.phaseName}:`, recoveryError);
      this.isRecovering = false;
      await this.abortAutomation(`Recovery failed: ${recoveryError.message}`);
    }
  }

  async recoverExtractProductDataPhase(phase, structuredError) {
    console.log(`[AutomationController] Recovering extract-product-data phase`);
    
    if (structuredError.errorType === 'timeout') {
      // For timeout, continue from where we left off
      console.log(`[AutomationController] Timeout recovery: resuming from product ${phase.currentProductIndex + 1}`);
      return await phase.execute(this.context);
    } else {
      // For other errors, retry current operation
      return await phase.retry(this.context);
    }
  }
}
```

### 4. Implementation Plan

#### Phase 1: Error Classification System
**Files to modify:**
- `tts-chrome-extension/background/errors/phase-errors.js` (new)
- `tts-chrome-extension/background/automation-controller.js`

#### Phase 2: Enhanced ExtractProductDataPhase
**Files to modify:**
- `tts-chrome-extension/background/phases/extract-product-data-phase.js`

#### Phase 3: Recovery Logic Enhancement
**Files to modify:**
- `tts-chrome-extension/background/automation-controller.js`

### 5. Expected Outcomes

#### Before Fix:
```
Product 15/20 timeout → extract-product-data timeout →
recovery fails → "search phase failed" → wrong recovery attempt
```

#### After Fix:
```
Product 15/20 timeout → skip to product 16/20 → continue extraction →
OR phase timeout → proper extract-product-data recovery → resume from current product
```

#### Benefits:
1. **Clear Error Attribution**: Errors are properly attributed to the actual failing phase
2. **Product-Level Resilience**: Individual product failures don't stop entire extraction
3. **Proper Recovery**: Recovery attempts target the correct phase with appropriate strategy
4. **Progress Preservation**: Timeout recovery resumes from current position instead of restarting
5. **Detailed Logging**: Enhanced debugging information for troubleshooting

### 6. Configuration Options

```javascript
// Enhanced configuration for extract-product-data phase
const config = {
  timeout: 300000,              // Total phase timeout (5 minutes)
  productTimeout: 30000,        // Per-product timeout (30 seconds)
  maxRetries: 2,               // Phase-level retries
  maxProductRetries: 2,        // Product-level retries
  skipFailedProducts: true,    // Skip failed products vs fail entire phase
  skipOnTimeout: true,         // Skip products that timeout
  skipOnNavigationError: true, // Skip products with navigation issues
  continueOnPhaseTimeout: true // Resume from current product on phase timeout
};
```

### 7. Monitoring and Metrics

#### Enhanced Progress Tracking
```javascript
// Progress report structure
{
  totalProducts: 20,
  extractedProducts: 12,
  skippedProducts: 2,
  failedProducts: 1,
  currentProduct: 15,
  successRate: 0.8,
  skipReasons: {
    timeout: 1,
    navigationError: 1,
    extractionError: 0
  }
}
```

This solution provides robust error handling, clear phase attribution, and resilient product extraction that can handle network variability while maintaining progress and providing detailed feedback.
