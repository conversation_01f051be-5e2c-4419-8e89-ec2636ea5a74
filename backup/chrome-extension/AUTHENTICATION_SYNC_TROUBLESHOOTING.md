# Authentication Sync Troubleshooting

This document helps troubleshoot authentication sync issues between the Chrome extension and the web application.

## How Authentication Sync Works

1. **Web App Authentication**: The web application uses NextAuth.js for session management
2. **Extension Authentication**: The extension tries to sync with the web app's authentication state
3. **Content Script Bridge**: A content script (`webapp-auth-sync.js`) runs on web app pages to bridge authentication state
4. **Automatic Sync**: When you open the extension popup, it automatically checks for existing web app authentication

## Troubleshooting Steps

### Step 1: Check Console Logs

1. Open the extension popup
2. Right-click in the popup and select "Inspect" to open DevTools
3. Check the Console tab for authentication-related messages:
   - `🔍 Checking for web app authentication...`
   - `Found X web app tabs:`
   - `✅ Found authenticated session in web app!` (success)
   - `❌ No authenticated web app sessions found` (failure)

### Step 2: Check Background Script Logs

1. Go to `chrome://extensions/`
2. Find "Product Crawler for TikTok Shop" extension
3. Click "service worker" link to open background script console
4. Look for authentication-related logs

### Step 3: Check Web App Tab

1. Make sure you have the web application open in a tab (`http://localhost:3000`)
2. Make sure you are logged in to the web application
3. Open DevTools on the web app tab and check Console for:
   - `WebApp Auth Sync content script loaded`
   - Any error messages from the content script

### Step 4: Manual Refresh

1. Close the extension popup
2. Refresh the web application page
3. Wait a few seconds for the content script to load
4. Open the extension popup again

### Step 5: Check Extension Permissions

1. Go to `chrome://extensions/`
2. Find "Product Crawler for TikTok Shop" extension
3. Make sure it has permission to access `http://localhost:3000/*`

## Common Issues and Solutions

### Issue: "No authenticated web app sessions found"

**Possible Causes:**
- Web app is not open in any tab
- User is not logged in to the web app
- Content script failed to load on the web app page
- NextAuth session is not accessible via the content script

**Solutions:**
1. Make sure you're logged in to the web app
2. Refresh the web app page
3. Check browser console for JavaScript errors
4. Try logging out and back in to the web app

### Issue: Content script not loading

**Possible Causes:**
- Extension permissions not granted
- Web app URL doesn't match manifest pattern
- Content script blocked by CSP

**Solutions:**
1. Check extension permissions in `chrome://extensions/`
2. Verify the web app is running on `http://localhost:3000`
3. Check for Content Security Policy errors in browser console

### Issue: Authentication works but user info is missing

**Possible Causes:**
- NextAuth session format is different than expected
- Access token is not available in the session

**Solutions:**
1. Check the NextAuth session structure in web app
2. Verify that the session includes user information
3. Update the content script to match your NextAuth configuration

## Debug Commands

You can run these commands in the browser console to debug:

```javascript
// In the web app tab console:
fetch('/api/auth/session').then(r => r.json()).then(console.log)

// In the extension popup console:
chrome.runtime.sendMessage({type: 'GET_AUTH_STATUS'}).then(console.log)
```

## Next Steps

If authentication sync still doesn't work after following these steps:

1. Check that your NextAuth configuration exposes the necessary session data
2. Verify that the web app's `/api/auth/session` endpoint returns user information
3. Consider implementing a custom authentication bridge endpoint in your web app
4. Test with a simple manual token passing mechanism as a fallback
