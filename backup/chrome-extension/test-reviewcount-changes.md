# Testing ReviewCount Migration

## Manual Testing Checklist

### 1. Backend API Testing

#### Test Create Crawled Product with reviewCount
```bash
# Test creating a product with reviewCount as number
curl -X POST http://localhost:3001/crawled-products \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "Test Product",
    "productUrl": "https://example.com/product/123",
    "marketplace": "etsy",
    "marketId": "etsy-123456",
    "sellerName": "TestSeller",
    "reviewCount": 127,
    "images": [
      {
        "imageUrl": "https://example.com/image.jpg",
        "isPrimary": true,
        "sortOrder": 0
      }
    ],
    "metadata": {
      "price": "$29.99",
      "currency": "USD",
      "rating": "4.8"
    }
  }'
```

#### Test Query with reviewCount filtering
```bash
# Test filtering by minimum review count
curl "http://localhost:3001/crawled-products?minReviewCount=50&maxReviewCount=500" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Test sorting by reviewCount
curl "http://localhost:3001/crawled-products?sortField=reviewCount&sortDirection=desc" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Expected Results:
- ✅ Product created successfully with `reviewCount` as direct property
- ✅ `reviewCount` not present in `metadata` object
- ✅ Filtering by review count works correctly
- ✅ Sorting by review count works correctly

### 2. Chrome Extension Testing

#### Test Amazon Product Page
1. Navigate to any Amazon product page
2. Open browser console
3. Run: `AmazonExtractor.extract()`
4. Check the returned data structure

**Expected Result:**
```javascript
{
  title: "Product Title",
  productUrl: "https://amazon.com/...",
  marketplace: "amazon",
  marketId: "amazon-B08...",
  sellerName: "Seller Name",
  reviewCount: 1234,  // ← Should be number, not string
  images: [...],
  metadata: {
    price: "$29.99",
    currency: "USD",
    rating: "4.5"
    // ← reviewCount should NOT be here
  }
}
```

#### Test Etsy Product Page
1. Navigate to any Etsy product page
2. Open browser console
3. Run: `EtsyExtractor.extract()`
4. Verify `reviewCount` is a number

#### Test eBay Product Page
1. Navigate to any eBay product page
2. Open browser console
3. Run: `EbayExtractor.extract()`
4. Verify `reviewCount` is a number

### 3. Frontend Testing

#### Test Search Page
1. Navigate to `/client/search`
2. Verify crawled products table displays correctly
3. Check that review count column shows numbers
4. Test sorting by review count
5. Test filtering by review count (if implemented)

**Expected Behavior:**
- ✅ Review counts display as numbers (e.g., "127", not "127 reviews")
- ✅ Sorting works numerically (10 comes after 9, not after 1)
- ✅ No console errors related to reviewCount

### 4. Database Verification

#### Check Schema
```sql
-- Verify reviewCount column exists
\d crawled_products

-- Should show:
-- reviewCount | integer | nullable
```

#### Check Data Migration
```sql
-- Check that existing data was migrated
SELECT id, title, "reviewCount", metadata 
FROM crawled_products 
WHERE "reviewCount" IS NOT NULL 
LIMIT 5;

-- Verify metadata no longer contains reviewCount
SELECT id, metadata 
FROM crawled_products 
WHERE metadata ? 'reviewCount';
-- Should return 0 rows
```

## Automated Testing

### Backend Unit Tests
```typescript
// Test in crawled-product.service.spec.ts
describe('CrawledProductService', () => {
  it('should create product with reviewCount as number', async () => {
    const dto = {
      title: 'Test Product',
      reviewCount: 127,
      // ... other fields
    };
    
    const result = await service.create(dto, userId);
    expect(result.reviewCount).toBe(127);
    expect(typeof result.reviewCount).toBe('number');
  });

  it('should filter by reviewCount range', async () => {
    const query = {
      minReviewCount: 50,
      maxReviewCount: 200
    };
    
    const result = await service.findAll(query, userId);
    result.data.forEach(product => {
      if (product.reviewCount !== null) {
        expect(product.reviewCount).toBeGreaterThanOrEqual(50);
        expect(product.reviewCount).toBeLessThanOrEqual(200);
      }
    });
  });
});
```

### Chrome Extension Tests
```javascript
// Test in browser console
describe('ReviewCount Extraction', () => {
  it('should extract reviewCount as number from Amazon', () => {
    const result = AmazonExtractor.extractReviewCountAsNumber();
    expect(typeof result).toBe('number');
    expect(result).toBeGreaterThan(0);
  });

  it('should parse comma-separated numbers correctly', () => {
    const result = CommonExtractor.parseReviewCountToNumber('1,234');
    expect(result).toBe(1234);
  });
});
```

## Troubleshooting

### Common Issues

1. **Migration fails**: Check database permissions and existing data format
2. **Chrome extension returns string**: Verify `extractReviewCountAsNumber()` is called
3. **Frontend displays NaN**: Check API response format
4. **Filtering doesn't work**: Verify query parameter names match backend

### Debug Commands

```bash
# Check backend logs
npm run start:dev

# Check database state
psql -d your_database -c "SELECT * FROM crawled_products LIMIT 1;"

# Test Chrome extension in isolation
CommonExtractor.parseReviewCountToNumber('1,234')
```

## Success Criteria

- ✅ All backend tests pass
- ✅ Chrome extension extracts reviewCount as numbers
- ✅ Frontend displays and sorts correctly
- ✅ Database migration completed successfully
- ✅ No breaking changes to existing functionality
- ✅ API documentation updated
- ✅ All TypeScript compilation succeeds
