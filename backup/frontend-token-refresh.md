# Frontend Token Refresh Implementation

This document outlines the token refresh implementation in the frontend of the TikTok Shop application.

## Overview

The frontend token refresh implementation works in conjunction with the backend to provide seamless authentication without requiring frequent re-logins. It uses NextAuth.js for session management and automatically refreshes tokens when they are about to expire.

## Key Components

1. **NextAuth.js Configuration**
   - Located in `src/app/api/auth/[...nextauth]/route.ts`
   - Handles token refresh during session management
   - Stores both access and refresh tokens

2. **Axios Interceptors**
   - Located in `src/lib/api/axios.ts`
   - Automatically adds authentication headers to requests
   - Handles 401 errors by refreshing tokens
   - Queues requests during token refresh

3. **Authentication Hooks**
   - `useNextAuth` in `src/lib/hooks/use-next-auth.ts`
   - `useAuth` in `src/lib/hooks/use-auth.ts`
   - Provide authentication state and methods to components

## Authentication Flow

1. **Login**
   - User logs in via Google OAuth or magic link
   - Backend returns access and refresh tokens
   - NextAuth stores tokens in the session

2. **API Requests**
   - Axios interceptor adds the access token to request headers
   - If a 401 error occurs, token refresh is triggered

3. **Token Refresh**
   - Happens automatically when tokens are about to expire
   - Occurs when a 401 error is received during an API request
   - New tokens are stored in the session

4. **Logout**
   - Revokes refresh tokens on the backend
   - Clears the session in NextAuth

## Implementation Details

### NextAuth.js Configuration

The NextAuth.js configuration includes:

- Token refresh logic in the `refreshAccessToken` function
- JWT callback to check token expiration and trigger refresh
- Session callback to expose tokens to the client

### Axios Interceptors

The Axios configuration includes:

- Request interceptor to add authentication headers
- Response interceptor to handle 401 errors
- Token refresh queue to prevent multiple refresh requests

### Authentication Hooks

The authentication hooks provide:

- Authentication state (isAuthenticated, isLoading)
- User information
- Login and logout methods
- Error handling for token refresh failures

## Error Handling

The implementation includes robust error handling:

- Token refresh failures trigger a logout
- API errors show user-friendly toast messages
- Session errors are captured and exposed to components

## Security Considerations

1. **Token Storage**
   - Access tokens are short-lived (15 minutes)
   - Refresh tokens are longer-lived but can be revoked
   - Tokens are stored in HTTP-only cookies when possible

2. **Token Rotation**
   - Refresh tokens are single-use
   - New refresh tokens are issued with each refresh

3. **Error Handling**
   - Failed refreshes trigger a logout
   - Suspicious activities can be monitored

## Integration with Backend

The frontend integration with the backend includes:

- Using the `/auth/refresh-token` endpoint for token refresh
- Sending the refresh token in the request body
- Handling the new token format (accessToken, refreshToken, expiresIn)

## Testing

To test the token refresh functionality:

1. Log in to the application
2. Wait for the access token to expire (or force expiration)
3. Make an API request and verify it succeeds
4. Check the network tab for the token refresh request

## Troubleshooting

Common issues and solutions:

- **Session Lost After Refresh**: Check that tokens are properly stored in the session
- **Frequent Logouts**: Check token expiration times and refresh logic
- **401 Errors**: Verify that tokens are being correctly added to requests
